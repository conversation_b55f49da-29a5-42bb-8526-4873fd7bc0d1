# Generated by Django 3.2.15 on 2022-11-24 14:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('User', '0001_initial'),
    ]

    operations = [
        migrations.DeleteModel(
            name='Device',
        ),
        migrations.RemoveField(
            model_name='user',
            name='password',
        ),
        migrations.RemoveField(
            model_name='user',
            name='type',
        ),
        migrations.AddField(
            model_name='user',
            name='appid',
            field=models.IntegerField(blank=True, default=0, verbose_name='appid'),
        ),
        migrations.AddField(
            model_name='user',
            name='deleted',
            field=models.IntegerField(blank=True, default=0, verbose_name='deleted'),
        ),
        migrations.AddField(
            model_name='user',
            name='device_id',
            field=models.CharField(blank=True, max_length=128, verbose_name='device_id'),
        ),
        migrations.AddField(
            model_name='user',
            name='jpush_id',
            field=models.Char<PERSON>ield(blank=True, max_length=256, verbose_name='jpush_id'),
        ),
        migrations.AddField(
            model_name='user',
            name='login_id',
            field=models.CharField(default='', max_length=256, verbose_name='login_id'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='user',
            name='login_type',
            field=models.IntegerField(default=0, verbose_name='login_type'),
        ),
        migrations.AddField(
            model_name='user',
            name='token',
            field=models.CharField(blank=True, max_length=128, verbose_name='token'),
        ),
    ]
