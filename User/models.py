from django.contrib import admin
from django.db import models


class UserManager(models.Manager):
    pass


# save idfa only when user signup, since indicates user source
class User(models.Model):
    name = models.CharField('name', max_length=50, default='')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = UserManager()


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    files = ('name', 'created_at', 'updated_at',)
    list_display = ('name', 'created_at', 'updated_at',)
    list_display_link = ('id',)
    search_fields = ('name',)
    list_per_page = 50
    objects = UserManager()
