# -*- coding: utf-8 -*-
import io
import sys

from selenium.webdriver.remote.webelement import WebElement

# 设置 stdout 为无缓冲模式
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', line_buffering=True)
import ctypes
import io
import json
import time
import traceback
# from PIL import ImageGrab
from datetime import datetime
import requests
import pyperclip
import win32clipboard
from PIL import Image
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
import re

savePath = "C:/Users/<USER>/Desktop/test/"


def jsz_image_upload(driver, args):
    gradient_xpath_click(args["taskid"], driver, "//img[@src='/sticsale/static/img/icon_10.056d12a.png']")
    # element = driver.find_element(By.XPATH, "//img[@src='/sticsale/static/img/icon_10.056d12a.png']")
    # element.click()
    # time.sleep(0.5)

    # 获取隐藏的 input 元素
    file_input = driver.find_element(By.ID, "ocr-input-car")

    gradient_css_click(args["taskid"], driver, ".vux-flexbox-item .positive")
    # front_img_element = driver.find_element(By.CSS_SELECTOR, ".vux-flexbox-item .positive")
    # front_img_element.click()  # 模拟点击触发 input
    time.sleep(0.2)  # 等待 JS 处理
    file_input.send_keys(args["jsz_image_path"])  # 上传文件
    time.sleep(0.5)  # 等待上传完成

    # 上传反面
    gradient_css_click(args["taskid"], driver, ".vux-flexbox-item .back")
    # back_img_element = driver.find_element(By.CSS_SELECTOR, ".vux-flexbox-item .back")
    # back_img_element.click()
    time.sleep(0.2)
    file_input.send_keys(args["jsz_image_path2"])
    time.sleep(0.5)

    ctypes.windll.user32.keybd_event(0x1B, 0, 0, 0)  # 按下 ESC
    ctypes.windll.user32.keybd_event(0x1B, 0, 2, 0)  # 松开 ESC
    ctypes.windll.user32.keybd_event(0x1B, 0, 0, 0)  # 按下 ESC
    ctypes.windll.user32.keybd_event(0x1B, 0, 2, 0)  # 松开 ESC
    # pyautogui.press('esc')
    # time.sleep(0.2)
    # pyautogui.press('esc')
    # element = driver.find_element(By.XPATH, "//img[@src='/sticsale/static/img/xszzm.ad6c56e.png']")
    # element.click()
    # pyperclip.copy(args["jsz_image_path"])
    # time.sleep(1.5)
    # pyautogui.hotkey("command", "shift", "g")
    # time.sleep(0.2)
    # pyautogui.hotkey("command", "shift", "g")  # 打开“前往文件夹”
    # time.sleep(0.2)
    # pyautogui.hotkey("command", "v")  # 粘贴路径
    # time.sleep(0.4)
    # pyautogui.press("enter")
    # time.sleep(0.4)
    # pyautogui.press("enter")  # 确认选择文件
    # time.sleep(0.4)

    # pyperclip.copy("")
    # # <img src="/sticsale/static/img/xszfm.2034785.png" class="ocr-base-img">
    # element = driver.find_element(By.XPATH, "//img[@src='/sticsale/static/img/xszfm.2034785.png']")
    # element.click()
    # pyperclip.copy(args["jsz_image_path2"])
    # time.sleep(1)
    # pyautogui.hotkey("command", "shift", "g")   # 打开“前往文件夹”
    # time.sleep(0.5)
    # pyautogui.hotkey("command", "v")  # 粘贴路径
    # time.sleep(0.4)
    # pyautogui.press("enter")
    # time.sleep(0.4)
    # pyautogui.press("enter")
    # time.sleep(0.4)

    # click div text
    gradient_xpath_click(args["taskid"], driver, "//div[contains(text(),'识别')]")
    # element = driver.find_element(By.XPATH, "//div[contains(text(),'识别')]")
    # element.click()

    # time.sleep(5)


replace_map = {
    "ROYA1": "ROYAL",
}


def correct_text(text: str, replace_map: dict) -> str:
    """
    按照替换 map 对字符串进行修正。
    优先匹配最长 key，避免子串冲突。
    """
    # 按 key 长度从大到小排序
    sorted_keys = sorted(replace_map.keys(), key=len, reverse=True)
    # 构建正则（自动转义）
    pattern = re.compile("|".join(re.escape(k) for k in sorted_keys))

    # 替换逻辑
    def _replace(m):
        return replace_map[m.group(0)]

    return pattern.sub(_replace, text)


def base_info_click(driver, args):
    # contains(@class,'vux-datetime-confirm') and text()='完成'
    element = WebDriverWait(driver, 5).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class,'car__input') and contains(text(),'转移登记日期')]")
        )
    )

    brandElement = driver.find_element(By.XPATH, '//input[@placeholder="品牌型号"]')
    brandStr = brandElement.get_attribute("value").strip()
    replaceStr = correct_text(brandStr.upper(), replace_map)
    if replaceStr != brandStr.upper():
        print(f"品牌型号修正：{brandStr} -> {replaceStr}")
        brandElement.send_keys(Keys.CONTROL + "a")
        brandElement.send_keys(Keys.DELETE)
        brandElement.send_keys(replaceStr)
    # 滚动到可见位置
    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)

    # time.sleep(2)
    if "djzy_date" in args and args["djzy_date"] != "":
        dj_date = args["djzy_date"]
        try:
            input_date = datetime.strptime(dj_date, "%Y-%m-%d").date()
            today = datetime.today().date()
            if input_date > today:
                return "❌ 转移登记日期不能大于今天"
        except ValueError as e:
            print(f"日期格式错误: {e}")
            return "❌ 日期格式错误，请使用 YYYY-MM-DD 格式"

        # element = WebDriverWait(driver, 10).until(
        #     EC.element_to_be_clickable(
        #         (By.XPATH, "//span[text()='转移登记日期']")
        #     )
        # )
        # link.click()

        # 滚动到可见位置
        # driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
        driver.execute_script("""arguments[0].classList.remove('vux-cell-placeholder');
        arguments[0].classList.add('vux-cell-value');arguments[0].textContent = arguments[1];
        arguments[0].dispatchEvent(new Event('change'));
        """, element, dj_date)
        print("-----------------------")
        time.sleep(0.1)
        # gradient_xpath_click(args["taskid"], driver, "//span[text()='转移登记日期']")
        # element = driver.find_element(By.XPATH, "//span[text()='转移登记日期']")
        element.click()
        time.sleep(0.1)

        gradient_xpath_click(args["taskid"], driver, "//div[contains(@class,'vux-datetime-confirm') and text()='完成']")
        # element = driver.find_element(By.XPATH, "//div[contains(@class,'vux-datetime-confirm') and text()='完成']")
        # element.click()
        time.sleep(0.2)

    if args.get("syx_gh", False):
        gradient_xpath_click(args["taskid"], driver, "//div[contains(text(),'商业过户')]")

        # element = driver.find_element(By.XPATH, "//div[contains(text(),'商业过户')]")
        # # 滚动到可见位置
        # # driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
        # element.click()
        # time.sleep(0.1)
    if args.get("jqx_gh", False):
        gradient_xpath_click(args["taskid"], driver, "//div[contains(text(),'交强过户')]")

        # element = driver.find_element(By.XPATH, "//div[contains(text(),'交强过户')]")
        # # 滚动到可见位置
        # # print("11111")
        # # driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
        # element.click()
        # # print("22222")
        # time.sleep(0.1)
    if args.get("is_white", False):
        gradient_xpath_click(args["taskid"], driver, "//div[contains(text(),'白名单')]")
        #
        # element = driver.find_element(By.XPATH, "//div[contains(text(),'白名单')]")
        # # 滚动到可见位置
        # # driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
        # element.click()
        # time.sleep(0.1)


def sfz_image_upload(driver, args, new_order):
    if new_order:
        gradient_css_click(args["taskid"], driver, "img.go-back.car-list-img-right:not([style])")
        # element = driver.find_element(By.CSS_SELECTOR, "img.go-back.car-list-img-right:not([style])")
        # element.click()
        # time.sleep(0.5)

        gradient_css_click(args["taskid"], driver, ".vux-flexbox-item .positive")
        # front_img_element = driver.find_element(By.CSS_SELECTOR, ".vux-flexbox-item .positive")
        # front_img_element.click()  # 模拟点击触发 input
        time.sleep(0.2)  # 等待 JS 处理
        # 获取隐藏的 input 元素
        file_input = driver.find_element(By.ID, "ocr-input-per")
        file_input.send_keys(args["sfz_image_path"])  # 上传文件
        time.sleep(0.5)  # 等待上传完成

        # 上传反面
        gradient_css_click(args["taskid"], driver, ".vux-flexbox-item .back")
        # back_img_element = driver.find_element(By.CSS_SELECTOR, ".vux-flexbox-item .back")
        # back_img_element.click()
        time.sleep(0.2)
        file_input.send_keys(args["sfz_image_path2"])
        time.sleep(0.5)

        ctypes.windll.user32.keybd_event(0x1B, 0, 0, 0)  # 按下 ESC
        ctypes.windll.user32.keybd_event(0x1B, 0, 2, 0)  # 松开 ESC
        ctypes.windll.user32.keybd_event(0x1B, 0, 0, 0)  # 按下 ESC
        ctypes.windll.user32.keybd_event(0x1B, 0, 2, 0)  # 松开 ESC

        # click div text
        gradient_xpath_click(args["taskid"], driver, "//div[contains(text(),'识别')]")
        # element = driver.find_element(By.XPATH, "//div[contains(text(),'识别')]")
        # element.click()
        # # time.sleep(3)

    waitEle = WebDriverWait(driver, 10).until(
        lambda d: d.find_element(By.XPATH, '//input[@placeholder="车主证件号"]').get_attribute("value") != ""
    )
    input_box = driver.find_element(By.XPATH, '//input[@placeholder="联系电话"]')
    input_box.clear()
    input_box.send_keys(args["phone_number"])

    owner_email = driver.find_element(By.NAME, "tprptCarOwnerDto-email-input")
    driver.execute_script("""
        arguments[0].value = arguments[1];
        arguments[0].dispatchEvent(new Event('input'));
        arguments[0].dispatchEvent(new Event('change'));
    """, owner_email, "<EMAIL>")


def tbr_image_upload(driver, args):
    if not args.get("tbr_not_chezhu", False):
        return
    pyperclip.copy("")

    gradient_xpath_click(args["taskid"], driver,
                         '//img[@class="go-back car-list-img-right" and contains(@style, "padding-left: 10px")]')
    # element = driver.find_element(By.XPATH,
    #                               '//img[@class="go-back car-list-img-right" and contains(@style, "padding-left: 10px")]')
    # element.click()
    # time.sleep(0.5)

    gradient_css_click(args["taskid"], driver, ".vux-flexbox-item .positive")
    # front_img_element = driver.find_element(By.CSS_SELECTOR, ".vux-flexbox-item .positive")
    # front_img_element.click()  # 模拟点击触发 input
    time.sleep(0.2)  # 等待 JS 处理
    # 获取隐藏的 input 元素
    file_input = driver.find_element(By.ID, "ocr-input-per")
    file_input.send_keys(args["tbr_sfz_image_path"])  # 上传文件
    time.sleep(0.5)  # 等待上传完成

    # 上传反面
    gradient_css_click(args["taskid"], driver, ".vux-flexbox-item .back")
    # back_img_element = driver.find_element(By.CSS_SELECTOR, ".vux-flexbox-item .back")
    # back_img_element.click()
    time.sleep(0.2)
    file_input.send_keys(args["tbr_sfz_image_path2"])
    time.sleep(0.5)

    ctypes.windll.user32.keybd_event(0x1B, 0, 0, 0)  # 按下 ESC
    ctypes.windll.user32.keybd_event(0x1B, 0, 2, 0)  # 松开 ESC
    ctypes.windll.user32.keybd_event(0x1B, 0, 0, 0)  # 按下 ESC
    ctypes.windll.user32.keybd_event(0x1B, 0, 2, 0)  # 松开 ESC

    gradient_xpath_click(args["taskid"], driver, "//div[contains(text(),'识别')]")
    # element = driver.find_element(By.XPATH, "//div[contains(text(),'识别')]")
    # element.click()
    # # time.sleep(2)
    input_box = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.NAME, "tprptApplicantDto-email-input")
        )
    )
    # input_box = driver.find_element(By.NAME, "tprptApplicantDto-email-input")
    # input_box.clear()
    # input_box.send_keys("<EMAIL>")
    driver.execute_script("""
        arguments[0].value = arguments[1];
        arguments[0].dispatchEvent(new Event('input'));
        arguments[0].dispatchEvent(new Event('change'));
    """, input_box, "<EMAIL>")


def baoxian_info_click(driver, args):
    if args.get("not_syx", False):
        gradient_xpath_click(args["taskid"], driver,
                             "//div[contains(@class,'car-offer-riskbtn') and normalize-space(text())='商业险']")

        # element = driver.find_element(By.XPATH,
        #                               "//div[contains(@class,'car-offer-riskbtn') and normalize-space(text())='商业险']")
        # element.click()
        # time.sleep(0.1)
    else:
        elements = driver.find_elements(By.CSS_SELECTOR, "span.main-risk-name")
        if args.get("not_csx", False):
            target = None
            for el in elements:
                if el.text.strip() == "车损险":
                    target = el
                    break
            if target:
                driver.execute_script("arguments[0].click();", target)
            else:
                raise Exception("未找到车损险")

        if "csx_use" in args:
            input_box = driver.find_element(By.CSS_SELECTOR, "input.weui-input[placeholder='保额']")
            input_box.click()
            input_box.send_keys(Keys.CONTROL, "a")
            input_box.send_keys(Keys.DELETE)
            input_box.send_keys(args["csx_use"])
            time.sleep(0.1)
        if args.get("not_sy_szx", False):
            target = None
            for el in elements:
                if el.text.strip() == "商业三者险":
                    target = el
                    break
            if target:
                driver.execute_script("arguments[0].click();", target)
            else:
                raise Exception("未找到商业三者险")
            # time.sleep(0.1)

        div_elements = driver.find_elements(By.CSS_SELECTOR, "div.sele-offer-item")
        subNum = 0
        if div_elements[2].text.strip() != "0":
            subNum = 1
        if "sy_szx_use" in args:
            if len(div_elements) >= 10 - subNum:
                div_elements[9 - subNum].click()
            else:
                raise Exception("未找到商业三者险输入源")
            # click_div = driver.find_element(By.XPATH, "//div[contains(@class,'sele-offer-item')]//span[normalize-space(text())='250万']")
            # click_div.click()
            gradient_xpath_click(args["taskid"], driver,
                                 "//div[contains(text(),'{0}') and contains(@class,'item-sele-def')]".format(
                                     args["sy_szx_use"]))
            # use_div = driver.find_element(By.XPATH,
            #                               "//div[contains(text(),'{0}') and contains(@class,'item-sele-def')]".format(
            #                                   args["sy_szx_use"]))
            # use_div.click()
            gradient_xpath_click(args["taskid"], driver,
                                 "//div[contains(@class,'car-com-btm-right') and contains(normalize-space(),'确定')]")
            # submit_div = driver.find_element(By.XPATH,
            #                                  "//div[contains(@class,'car-com-btm-right') and contains(normalize-space(),'确定')]")
            # submit_div.click()

        if args.get("not_zwx_siji", False):
            target = None
            for el in elements:
                if el.text.strip() == "座位险(司机)":
                    target = el
                    break
            if target:
                driver.execute_script("arguments[0].click();", target)
            else:
                raise Exception("未找到座位险(司机)")
        if "zwx_siji_use" in args:
            if len(div_elements) >= 15 - subNum:
                div_elements[14 - subNum].click()
            else:
                raise Exception("未找到座位险(司机)输入源")

            gradient_xpath_click(args["taskid"], driver,
                                 "//div[contains(text(),'{0}') and contains(@class,'item-sele-def')]".format(
                                     args["zwx_siji_use"]))
            # use_div = driver.find_element(By.XPATH,
            #                               "//div[contains(text(),'{0}') and contains(@class,'item-sele-def')]".format(
            #                                   args["zwx_siji_use"]))
            # use_div.click()

            gradient_xpath_click(args["taskid"], driver,
                                 "//div[contains(@class,'car-com-btm-right') and contains(normalize-space(),'确定')]")
            # submit_div = driver.find_element(By.XPATH,
            #                                  "//div[contains(@class,'car-com-btm-right') and contains(normalize-space(),'确定')]")
            # submit_div.click()
        if args.get("not_zwx_chengke", False):
            target = None
            for el in elements:
                if el.text.strip() == "座位险(乘客)":
                    target = el
                    break
            if target:
                driver.execute_script("arguments[0].click();", target)
            else:
                raise Exception("未找到座位险(乘客)")
        if "zwx_chengke_use" in args:
            if len(div_elements) >= 18 - subNum:
                div_elements[17 - subNum].click()
            else:
                raise Exception("未找到座位险(乘客)输入源")

            gradient_xpath_click(args["taskid"], driver,
                                 "//div[contains(text(),'{0}') and contains(@class,'item-sele-def')]".format(
                                     args["zwx_chengke_use"]))
            # use_div = driver.find_element(By.XPATH,
            #                               "//div[contains(text(),'{0}') and contains(@class,'item-sele-def')]".format(
            #                                   args["zwx_chengke_use"]))
            # use_div.click()

            gradient_xpath_click(args["taskid"], driver,
                                 "//div[contains(@class,'car-com-btm-right') and contains(normalize-space(),'确定')]")
            # submit_div = driver.find_element(By.XPATH,
            #                                  "//div[contains(@class,'car-com-btm-right') and contains(normalize-space(),'确定')]")
            # submit_div.click()

        if not args.get("not_sy_szx", False) and args.get("is_fujia_yiliao", False):
            target = None
            for el in elements:
                if el.text.strip() == "附加医保-三者":
                    target = el
                    break
            if target:
                driver.execute_script("arguments[0].click();", target)
            else:
                raise Exception("未找到附加医保-三者")
        if "fujia_yiliao_use" in args:
            if len(div_elements) >= 21 - subNum:
                div_elements[20 - subNum].click()
            else:
                raise Exception("未找到附加医保-三者输入源")

            gradient_xpath_click(args["taskid"], driver,
                                 "//div[contains(text(),'{0}') and contains(@class,'item-sele-def')]".format(
                                     args["fujia_yiliao_use"]))
            # use_div = driver.find_element(By.XPATH,
            #                               "//div[contains(text(),'{0}') and contains(@class,'item-sele-def')]".format(
            #                                   args["fujia_yiliao_use"]))
            # use_div.click()

            gradient_xpath_click(args["taskid"], driver,
                                 "//div[contains(@class,'car-com-btm-right') and contains(normalize-space(),'确定')]")
            # submit_div = driver.find_element(By.XPATH,
            #                                  "//div[contains(@class,'car-com-btm-right') and contains(normalize-space(),'确定')]")
            # submit_div.click()

    if args.get("not_jqx", False):
        gradient_xpath_click(args["taskid"], driver,
                             "//div[contains(@class,'car-offer-riskbtn') and normalize-space(text())='交强险']")
        # element = driver.find_element(By.XPATH,
        #                               "//div[contains(@class,'car-offer-riskbtn') and normalize-space(text())='交强险']")
        # element.click()
        # time.sleep(0.1)

    gradient_xpath_click(args["taskid"], driver,
                         "//div[contains(@class,'car_rightbtn') and contains(normalize-space(),'保费计算')]")
    # submit = driver.find_element(By.XPATH,
    #                              "//div[contains(@class,'car_rightbtn') and contains(normalize-space(),'保费计算')]")
    # submit.click()
    # # time.sleep(2)


def baoxian_info2_click(driver, args):
    element = WebDriverWait(driver, 20).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class,'task-companyName') and contains(normalize-space(),'泰康在线')]")
        )
    )
    ctypes.windll.user32.keybd_event(0x1B, 0, 0, 0)  # 按下 ESC
    ctypes.windll.user32.keybd_event(0x1B, 0, 2, 0)  # 松开 ESC
    time.sleep(0.2)
    # element = driver.find_element(By.XPATH, "//div[contains(@class,'task-companyName') and contains(normalize-space(),'泰康在线')]")
    gradient_xpath_click(args["taskid"], driver,
                         "//div[contains(@class,'task-companyName') and contains(normalize-space(),'泰康在线')]")
    # element.click()
    # time.sleep(0.3)

    gradient_xpath_script(args["taskid"], driver, "//span[normalize-space(text())='请选择']",
                          "arguments[0].scrollIntoView({block: 'center'});")
    # toubaodi_element = driver.find_element(By.XPATH, "//span[normalize-space(text())='请选择']")
    # driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", toubaodi_element)

    gradient_xpath_click(args["taskid"], driver, "//span[normalize-space(text())='请选择']")
    # time.sleep(0.5)
    # toubaodi_element.click()

    input_box = gradient_css_click(args["taskid"], driver, "input.weui-input[placeholder='输入搜索']")
    # input_box = driver.find_element(By.CSS_SELECTOR, "input.weui-input[placeholder='输入搜索']")
    # input_box.click()
    input_box.send_keys(args["toubaodi"])
    input_box.send_keys(Keys.ENTER)

    gradient_xpath_click(args["taskid"], driver,
                         "//div[contains(@class,'car-style') and contains(normalize-space(),'{0}')]".format(
                             args["toubaodi"]))
    # select_elememt = driver.find_element(By.XPATH,
    #                                      "//div[contains(@class,'car-style') and contains(normalize-space(),'{0}')]".format(
    #                                          args["toubaodi"]))
    # select_elememt.click()

    gradient_xpath_click(args["taskid"], driver,
                         "//div[contains(@class,'tabbar_item on') and contains(normalize-space(),'确定')]")
    # submit_div = driver.find_element(By.XPATH,
    #                                  "//div[contains(@class,'tabbar_item on') and contains(normalize-space(),'确定')]")
    # submit_div.click()

    gradient_xpath_click(args["taskid"], driver,
                         "//a[contains(@class,'com-sele--title') and contains(normalize-space(.),'点击获取交叉销售')]")
    # info_element = driver.find_element(By.XPATH,
    #                                    "//a[contains(@class,'com-sele--title') and contains(normalize-space(.),'点击获取交叉销售')]")
    # info_element.click()

    link = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class,'sele-more-info') and contains(text(),'款-') and contains(text(),'元')]")
        )
    )
    link.click()
    time.sleep(0.1)
    if args.get("not_syx", False):
        # 只有交强
        gradient_xpath_click(args["taskid"], driver, "//span[contains(text(),'E款-')]")
        # click_ele = driver.find_element(By.XPATH, "//span[contains(text(),'E款-')]")
        # click_ele.click()
    else:
        gradient_xpath_click(args["taskid"], driver, "//span[contains(text(),'D款-')]")
        # click_ele = driver.find_element(By.XPATH, "//span[contains(text(),'D款-')]")
        # click_ele.click()

    confirm_list = driver.find_elements(By.XPATH, "//p[@class='label' and normalize-space(text())='确定']")
    confirm_list[len(confirm_list) - 1].click()

    gradient_xpath_click(args["taskid"], driver,
                         "//div[contains(@class,'vux-flexbox-item') and contains(normalize-space(),'保费计算')]")
    # compute_element = driver.find_element(By.XPATH,
    #                                       "//div[contains(@class,'vux-flexbox-item') and contains(normalize-space(),'保费计算')]")
    # compute_element.click()
    pass


def save_png(driver, write_path):
    link = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class,'share-item') and contains(text(),'报价单分享')]")
        )
    )
    link.click()
    time.sleep(1)
    share_link = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class,'carQuotation-close') and contains(text(),'复制报价单')]")
        )
    )
    share_link.click()
    time.sleep(1)

    win32clipboard.OpenClipboard()
    try:
        data = win32clipboard.GetClipboardData(win32clipboard.CF_DIB)
        if data:
            image = Image.open(io.BytesIO(data))
            image.save(write_path, "PNG")
            print(f"✅ 已保存: {write_path}")
        else:
            print("❌ 剪贴板没有图片")
    finally:
        win32clipboard.CloseClipboard()
    pass


def chongxin_toubao(driver, args, jiaoqiang, shangye):
    if jiaoqiang:
        args["not_jqx"] = True
    if shangye:
        args["not_syx"] = True

    gradient_xpath_click(args["taskid"], driver,
                         "//div[contains(@class,'vux-flexbox-item') and normalize-space(text())='修改险种']")
    # btnEle = driver.find_element(By.XPATH,
    #                              "//div[contains(@class,'vux-flexbox-item') and normalize-space(text())='修改险种']")
    # btnEle.click()
    # time.sleep(0.5)
    baoxian_info_click(driver, args)
    baoxian_info2_click(driver, args)
    share_click(driver, args)


def notify_task_result(taskid: str, res_text: str, result_filepath: str):
    url = "http://127.0.0.1:80/aibot/task/result/notify/"
    headers = {"Content-Type": "application/json"}
    data = {
        "taskid": taskid,
        "res_text": res_text,
        "result_filepath": result_filepath
    }

    try:
        response = requests.post(url, headers=headers, data=json.dumps(data), timeout=5)
        print("Status Code:", response.status_code)
        # print("Response:", response.text)
        print("Response:", data)
        return response
    except requests.exceptions.RequestException as e:
        print("请求失败:", e)
        return None


def share_click(driver, args):
    savePng = savePath + args["taskid"] + ".png"
    # time.sleep(5)
    link = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.XPATH,
                                    "//button[contains(@class,'weui-btn_plain-default') and contains(normalize-space(.), '快速修改')]")
                                   )
    )
    tables = driver.find_elements(By.XPATH, '//div[@class="table-view-cell"]')
    print("get cell=", len(tables))
    for table in tables:
        # 判断是否是泰康
        try:
            checkEle = table.find_element(By.XPATH,
                                          ".//div[contains(@class,'mui-table-cell') and contains(normalize-space(.), '泰康在线')]")
        except:
            continue
        # print("is taikang")
        # 判断是否失败
        isFail = False
        try:
            failEle = table.find_element(By.XPATH,
                                         ".//div[contains(@class,'item-title') and contains(normalize-space(.), '报价失败')]")
            isFail = True
        except Exception as e:
            print("is fail=", e)
            pass
        if isFail:
            print("has fail")
            # 报价失败尝试修改
            lookEle = table.find_element(By.XPATH, ".//span[contains(text(), '查看原因')]")
            lookEle.click()
            failRes = ""
            for i in range(3):
                try:
                    failResEle = table.find_element(By.XPATH, ".//div[contains(@class,'showDetail')]")
                    failRes = failResEle.text
                    break
                except:
                    lookEle.click()

            print("报价失败，原因: " + failRes)
            if "交强险重复投保" in failRes:
                print("交强险重复投保，重新修改")
                chongxin_toubao(driver, args, True, False)
            if "商业险重复投保" in failRes:
                print("商业险重复投保，重新修改")
                chongxin_toubao(driver, args, False, True)

            failPng = savePng.replace(".png", "_fail.png")
            table.screenshot(failPng)
            print("save screenshot=" + failPng)
            notify_task_result(args["taskid"], failRes, failPng)
            pass
        else:
            # 不在更多操作里面的分享报价
            has_share = True
            try:
                gradient_xpath_click(args["taskid"], table, ".//button[contains(normalize-space(.), '分享报价')]")
                # element = table.find_element(By.XPATH, ".//button[contains(normalize-space(.), '分享报价')]")
                # element.click()
            except:
                has_share = False
                pass
            if not has_share:
                # 在更多操作里面的分享报价
                gradient_xpath_click(args["taskid"], table, ".//button[contains(normalize-space(.), '更多操作')]")
                # element = table.find_element(By.XPATH, ".//button[contains(normalize-space(.), '更多操作')]")
                # element.click()

                link = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable(
                        (By.XPATH,
                         "//div[contains(@class,'more-btn-item') and contains(normalize-space(.), '分享报价')]")
                    )
                )
                # element = driver.find_element(By.XPATH, "//div[contains(@class,'more-btn-item') and contains(normalize-space(.), '分享报价')]")
                link.click()

            save_png(driver, savePng)
            notify_task_result(args["taskid"], "success", savePng)


def rpa_robot(url, args):
    user_data_dir = "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data"

    # 启动浏览器（这里用 Chrome，你需要先安装对应的 chromedriver）
    options = webdriver.ChromeOptions()
    options.binary_location = "C:\\chrome_driver\\chrome-win64\\chrome-win64\\chrome.exe"
    # options.debugger_address = "127.0.0.1:9222"
    options.add_argument("--start-maximized")  # 最大化窗口
    options.add_argument(f"--user-data-dir={user_data_dir}")  # 使用指定的s用户数据目录
    options.add_argument("--profile-directory=Default")  # 使用默认用户配置

    service = Service(r"C:\\chrome_driver\\chromedriver.exe")
    driver = webdriver.Chrome(service=service, options=options)

    try:
        # 1. 打开网页
        # driver.new_window()

        driver.get(url)
        time.sleep(1)

        gradient_xpath_click(args["taskid"], driver, "//span[text()='首页']")
        # element = driver.find_element(By.XPATH, "//span[text()='首页']")
        # element.click()
        # time.sleep(0.2)

        gradient_xpath_click(args["taskid"], driver, "//div[@class='vux-tab-item' and contains(text(),'自主报价')]")
        # element = driver.find_element(By.XPATH, "//div[@class='vux-tab-item' and contains(text(),'自主报价')]")
        # element.click()
        # time.sleep(0.2)

        jsz_image_upload(driver, args)

        # is new order
        new_order = True
        try:
            link = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable(
                    (By.XPATH,
                     "//a[contains(@class,'weui-dialog__btn weui-dialog__btn_default') and contains(normalize-space(.),'新建订单')]")
                )
            )
            link.click()
            time.sleep(3)
            link = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable(
                    (By.XPATH,
                     "//a[contains(@class,'weui-dialog__btn weui-dialog__btn_default') and contains(normalize-space(.),'新建订单')]")
                )
            )
            link.click()
            new_order = False
            print("has old order")
            pass
        except Exception as e:
            print("new order")

        base_info_click(driver, args)

        sfz_image_upload(driver, args, new_order)

        tbr_image_upload(driver, args)

        # driver.execute_script("document.body.focus();")
        # driver.execute_script("document.activeElement.blur();")
        element = driver.find_element(By.XPATH,
                                      "//div[contains(@class,'car-btn-per') and contains(@class,'car-btn-div') and contains(text(),'同车主')]")
        # 滚动到可见位置
        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
        time.sleep(0.1)
        if args["bbr_with_chezhu"]:
            # pyautogui.press('down', presses=3, interval=0.2)
            # pyautogui.scroll(-1300)
            driver.execute_script("""
            var el = document.scrollingElement || document.documentElement || document.body;
            el.scrollTop = el.scrollHeight;
            window.scrollTo(0, el.scrollHeight);
            """)
            time.sleep(0.2)
            element.click()

        gradient_xpath_click(args["taskid"], driver, "//div[contains(text(),'下一步')]")
        # element = driver.find_element(By.XPATH, "//div[contains(text(),'下一步')]")
        # element.click()
        # time.sleep(0.3)

        # second_car = driver.find_element(
        #     By.XPATH,
        #     "(//div[contains(@class,'weui-cell') and contains(@class,'car-cell')])[2]"
        # )
        # second_car.click()
        # time.sleep(0.2)
        # <div class="car__input">
        #   品牌型号
        # </div>
        div = WebDriverWait(driver, 4).until(
            EC.visibility_of_element_located((By.XPATH, '//div[@class="car__input" and contains(text(), "品牌型号")]'))
        )
        # time.sleep(1)
        gradient_xpath_click(args["taskid"], driver, "//div[contains(text(),'下一步')]")
        # nextEle = driver.find_element(By.XPATH, "//div[contains(text(),'下一步')]")
        # nextEle.click()
        # time.sleep(0.5)
        # 定位输入框
        input_box = WebDriverWait(driver, 4).until(
            lambda d: d.find_element(By.XPATH, '//input[@placeholder="核定座位"]')
        )

        input_box = driver.find_element(By.XPATH, '//input[@placeholder="核定座位"]')

        if "site_number" in args:
            # 清空内容
            input_box.click()
            input_box.send_keys(Keys.CONTROL, "a")
            input_box.send_keys(Keys.DELETE)
            input_box.clear()
            # 输入新内容，例如 "5"
            input_box.send_keys(args["site_number"])
            time.sleep(0.2)

        gradient_xpath_click(args["taskid"], driver, "//div[contains(text(),'下一步')]")
        # element = driver.find_element(By.XPATH, "//div[contains(text(),'下一步')]")
        # element.click()
        time.sleep(0.5)

        baoxian_info_click(driver, args)

        baoxian_info2_click(driver, args)

        share_click(driver, args)

        print("success!")
    except Exception as e:
        print("failed")
        traceback.print_exc()
    finally:
        driver.quit()


def gradient_css_click(taskid: str, driver, css_selector, max_attempts=10) -> WebElement:
    """
    梯度尝试点击元素的函数

    Args:
        taskid:
        driver: WebDriver实例
        css_selector: 元素的 css 定位器
        max_attempts: 最大尝试次数，默认10次

    Returns:
        bool: 点击成功返回True，失败返回False
    """
    for attempt in range(max_attempts):
        try:
            # 第一次尝试直接点击
            if attempt == 0:
                element = driver.find_element(By.CSS_SELECTOR, css_selector)
                element.click()
                return element

            # 后续尝试使用显式等待
            element = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, css_selector))
            )
            element.click()
            return element

        except Exception as e:
            print(f"[gradient_css_click] Try: {attempt + 1} failed: {str(e)}")
            if attempt < max_attempts - 1:  # 不是最后一次尝试
                time.sleep(0.3)  # 等待后重试
                continue
            else:
                print(f"[gradient_css_click] try {max_attempts} and final failed: {css_selector}")
                notify_task_result(taskid, f"gradient_css_click failed: {str(e)}", "")
                raise Exception(f"[gradient_css_click] failed to gradient_css_click: {css_selector}")

    return None


def gradient_xpath_click(taskid: str, driver, xpath, max_attempts=10):
    """
    梯度尝试点击元素的函数

    Args:
        taskid: 任务id
        driver: WebDriver实例
        xpath: 元素的XPath定位器
        max_attempts: 最大尝试次数，默认10次

    Returns:
        bool: 点击成功返回True，失败返回False
    """
    for attempt in range(max_attempts):
        try:
            # 第一次尝试直接点击
            if attempt == 0:
                element = driver.find_element(By.XPATH, xpath)
                element.click()
                return True

            # 后续尝试使用显式等待
            element = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, xpath))
            )
            element.click()
            return True

        except Exception as e:
            print(f"[gradient_xpath_click] Try: {attempt + 1} failed: {str(e)}")
            if attempt < max_attempts - 1:  # 不是最后一次尝试
                time.sleep(0.3)  # 等待后重试
                continue
            else:
                print(f"[gradient_xpath_click] try {max_attempts} and final failed: {xpath}")
                notify_task_result(taskid, f"gradient_xpath_click failed: {str(e)}", "")
                raise Exception(f"[gradient_xpath_click] failed to gradient_xpath_click: {xpath}")

    return False


def gradient_xpath_script(taskid: str, driver, xpath, script, max_attempts=10):
    """
    梯度尝试执行script的函数

    Args:
        taskid: 任务id
        driver: WebDriver实例
        xpath: 元素的XPath定位器
        script: 脚本
        max_attempts: 最大尝试次数，默认10次

    Returns:
        bool: 点击成功返回True，失败返回False
    """
    for attempt in range(max_attempts):
        try:
            element = driver.find_element(By.XPATH, xpath)
            driver.execute_script(script, element)
            return True
        except Exception as e:
            print(f"[gradient_xpath_script] Try: {attempt + 1} failed: {str(e)}")
            if attempt < max_attempts - 1:  # 不是最后一次尝试
                time.sleep(0.3)  # 等待后重试
                continue
            else:
                print(f"[gradient_xpath_script] try {max_attempts} and final failed: {xpath}, script:{script}")
                notify_task_result(taskid, f"gradient_xpath_script failed: {str(e)}", "")
                raise Exception(f"[gradient_xpath_script] failed to find: {xpath}, script:{script}")

    return False


if __name__ == "__main__":
    json_str = """
    {"jsz_image_path": "C:/Users/<USER>/Desktop/test/3.jpg",
    "jsz_image_path2": "C:/Users/<USER>/Desktop/test/3.jpg",
    "djzy_date": "", "jqx_gh":false, "syx_gh":false, "is_white":false,
    "sfz_image_path": "C:/Users/<USER>/Desktop/test/1.jpg",
    "sfz_image_path2": "C:/Users/<USER>/Desktop/test/2.jpg",
    "phone_number": "13418299452",
    "tbr_not_chezhu":false, "bbr_with_chezhu":true,
    "tbr_sfz_image_path": "/Users/<USER>/Downloads/other_sfz.jpg",
    "tbr_sfz_image_path2": "/Users/<USER>/Downloads/other_sfz2.jpg",
    "site_number": "5",
    "not_syx": false,
    "sy_szx_use": "250万",
    "not_jqx": false,
    "toubaodi": "揭阳市",
    "share_name": "C:/Users/<USER>/Desktop/test/bjd3.png",
    "taskid": "123"
    }
    """

    url = "https://m.ins10.cn/#/sticsale/agent?tabbar=true&icon=true"  # 目标网页
    args = json.loads(json_str)
    rpa_robot(url, args)
