# Generated by Django 3.2.7 on 2025-09-12 07:17

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SMSRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_id', models.<PERSON>r<PERSON><PERSON>(max_length=64)),
                ('latest_ts', models.BigIntegerField()),
                ('direction', models.Char<PERSON>ield(max_length=20)),
                ('from_number', models.Char<PERSON>ield(max_length=50)),
                ('to_number', models.CharField(max_length=50)),
                ('content', models.TextField(blank=True)),
                ('images', models.Char<PERSON>ield(blank=True, max_length=2000)),
                ('link', models.Char<PERSON>ield(blank=True, max_length=2000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_image', models.Integer<PERSON>ield(blank=True, default=0)),
            ],
        ),
    ]
