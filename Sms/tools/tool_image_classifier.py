from typing import Dict, List, Tuple

from AiInsuranceBot.settings import logger


class ImageClassifier:
    """
    图片分类器，用于识别身份证、驾驶证等证件类型
    """

    # 证件类型常量
    ID_CARD = "identity_card"  # 身份证
    DRIVER_LICENSE = "driver_license"  # 驾驶证
    UNKNOWN = "unknown"  # 未知类型

    # 身份证关键词
    ID_CARD_KEYWORDS = [
        "身份证", "居民身份证", "中华人民共和国居民身份证",
        "姓名", "性别", "民族", "出生", "住址", "公民身份号码",
        "签发机关", "有效期限"
    ]

    # 驾驶证关键词
    DRIVER_LICENSE_KEYWORDS = [
        "驾驶证", "机动车驾驶证", "中华人民共和国机动车驾驶证",
        "驾驶证号", "准驾车型", "有效期限", "初次领证日期",
        "档案编号", "记录"
    ]

    @staticmethod
    def classify_image_by_content(image_content: str) -> Dict[str, any]:
        """
        通过图片内容分析图片类型
        
        Args:
            image_content: base64编码的图片内容
            
        Returns:
            Dict包含分类结果和置信度
        """
        try:
            # 这里可以集成OCR服务来识别图片中的文字
            # 目前先使用简单的规则匹配

            # 模拟OCR识别结果（实际项目中应该调用真实的OCR API）
            ocr_text = ImageClassifier._mock_ocr_recognition(image_content)

            # 分析文字内容
            classification = ImageClassifier._analyze_text_content(ocr_text)

            return classification

        except Exception as e:
            logger.error(f"[ImageClassifier] Error classifying image: {e}", exc_info=True)
            return {
                "type": ImageClassifier.UNKNOWN,
                "confidence": 0.0,
                "details": "分类失败",
                "error": str(e)
            }

    @staticmethod
    def _mock_ocr_recognition(image_content: str) -> str:
        """
        模拟OCR识别（实际项目中应该替换为真实的OCR API调用）
        
        Args:
            image_content: base64编码的图片内容
            
        Returns:
            识别出的文字内容
        """
        # 这里应该调用真实的OCR服务，比如：
        # - 百度OCR API
        # - 腾讯云OCR API
        # - 阿里云OCR API
        # - Google Vision API
        # 等等

        # 目前返回空字符串，实际使用时需要实现真实的OCR调用
        logger.info("[ImageClassifier] Mock OCR recognition - should implement real OCR API")
        return ""

    @staticmethod
    def _analyze_text_content(text: str) -> Dict[str, any]:
        """
        分析文字内容，判断证件类型
        
        Args:
            text: OCR识别出的文字内容
            
        Returns:
            分类结果
        """
        if not text:
            return {
                "type": ImageClassifier.UNKNOWN,
                "confidence": 0.0,
                "details": "无法识别图片中的文字内容"
            }

        # 计算身份证关键词匹配度
        id_card_score = 0
        id_card_matches = []
        for keyword in ImageClassifier.ID_CARD_KEYWORDS:
            if keyword in text:
                id_card_score += 1
                id_card_matches.append(keyword)

        # 计算驾驶证关键词匹配度
        driver_license_score = 0
        driver_license_matches = []
        for keyword in ImageClassifier.DRIVER_LICENSE_KEYWORDS:
            if keyword in text:
                driver_license_score += 1
                driver_license_matches.append(keyword)

        # 判断类型
        if id_card_score > driver_license_score and id_card_score > 0:
            confidence = min(id_card_score / len(ImageClassifier.ID_CARD_KEYWORDS), 1.0)
            return {
                "type": ImageClassifier.ID_CARD,
                "confidence": confidence,
                "details": f"识别为身份证，匹配关键词: {', '.join(id_card_matches)}",
                "matched_keywords": id_card_matches
            }
        elif driver_license_score > id_card_score and driver_license_score > 0:
            confidence = min(driver_license_score / len(ImageClassifier.DRIVER_LICENSE_KEYWORDS), 1.0)
            return {
                "type": ImageClassifier.DRIVER_LICENSE,
                "confidence": confidence,
                "details": f"识别为驾驶证，匹配关键词: {', '.join(driver_license_matches)}",
                "matched_keywords": driver_license_matches
            }
        else:
            return {
                "type": ImageClassifier.UNKNOWN,
                "confidence": 0.0,
                "details": "无法确定证件类型，未匹配到足够的关键词"
            }

    @staticmethod
    def classify_image_by_filename(filename: str) -> Dict[str, any]:
        """
        通过文件名分析图片类型（辅助方法）
        
        Args:
            filename: 文件名
            
        Returns:
            分类结果
        """
        if not filename:
            return {
                "type": ImageClassifier.UNKNOWN,
                "confidence": 0.0,
                "details": "文件名为空"
            }

        filename_lower = filename.lower()

        # 检查文件名中的关键词
        if any(keyword in filename_lower for keyword in ["身份证", "idcard", "id_card", "sfz"]):
            return {
                "type": ImageClassifier.ID_CARD,
                "confidence": 0.7,
                "details": "根据文件名判断为身份证"
            }
        elif any(keyword in filename_lower for keyword in ["驾驶证", "driver", "license", "jsz"]):
            return {
                "type": ImageClassifier.DRIVER_LICENSE,
                "confidence": 0.7,
                "details": "根据文件名判断为驾驶证"
            }
        else:
            return {
                "type": ImageClassifier.UNKNOWN,
                "confidence": 0.0,
                "details": "文件名中未包含证件类型关键词"
            }

    @staticmethod
    def classify_multiple_images(images: List[Tuple[str, str]]) -> List[Dict[str, any]]:
        """
        批量分类多张图片
        
        Args:
            images: 图片列表，每个元素是(filename, base64_content)的元组
            
        Returns:
            分类结果列表
        """
        results = []

        for filename, content in images:
            try:
                # 首先尝试通过内容分析
                content_result = ImageClassifier.classify_image_by_content(content)
                content_result["filename"] = filename
                results.append(content_result)

            except Exception as e:
                logger.error(f"[ImageClassifier] Error classifying image {filename}: {e}", exc_info=True)
                results.append({
                    "filename": filename,
                    "type": ImageClassifier.UNKNOWN,
                    "confidence": 0.0,
                    "details": f"分类失败: {str(e)}",
                    "error": str(e)
                })

        return results


if __name__ == '__main__':
    print(ImageClassifier.classify_multiple_images(["身份证.jpg"]))
