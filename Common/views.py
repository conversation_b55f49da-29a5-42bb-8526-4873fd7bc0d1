
import json
import time

from django.conf import settings
from django.http import HttpResponse, HttpResponsePermanentRedirect, JsonResponse
from django.views.generic.base import View

from Common.err import ErrInfo
from Common.util import Util
from AiInsuranceBot.settings import logger


class CommonView(View):

    def GetOrNone(self, model, **kwargs):

        try:
            return model.objects.get(**kwargs)
        except model.DoesNotExist:
            return None

    def ReturnError(self, err_code, err_msg=None):

        if err_msg is None:
            # Get language from request headers
            headers = self.GetHeaderInRequest(self.request)
            lang = headers.get('lang', 'en')

            # Select message dictionary based on language
            msg_dic = getattr(ErrInfo, f'ErrMsg_{lang.upper()}', ErrInfo.ErrMsg_EN)
            err_msg = msg_dic.get(err_code, "err msg undefined")

        return self.ReturnJson({"err_code": err_code, "err_msg": err_msg, "data": {}})

    def ReturnSuccess(self, data=None):

        data = {} if data is None else data
        return self.ReturnJson({"err_code": 0, "err_msg": "success", "data": data})

    def ReturnJson(self, args):

        return JsonResponse(args, safe=False)

    def ReadPostJson(self, request, check_list=()):

        try:
            data = json.loads(request.body)
        except Exception as e:
            return None, ErrInfo.JSON_FORMAT_ERROR, str(e)

        for item in check_list:
            if item not in data:
                logger.error(f"ReadPostJson missing param: {item}")
                return None, ErrInfo.JSON_REQUEST_PARA_MISS, "miss parameter: " + item

        return data, 0, None

    def ReturnStr(self, content):

        return HttpResponse(content)

    def RedirectPage(self, url):

        return HttpResponsePermanentRedirect(url)

    def GetHeaderInRequest(self, request):

        h = request.META
        headers = {
            "userid": h.get('HTTP_X_USERID', None),
            "token": h.get('HTTP_X_TOKEN', None),
            "timestamp": h.get('HTTP_X_TIMESTAMP', None),
            "sign": h.get('HTTP_X_SIGN', None),
            "os": h.get('HTTP_X_OS', None),
            "lang": h.get('HTTP_X_LANG', ''),
            "idfa": h.get('HTTP_X_IDFA', ''),
            "device_id": h.get('HTTP_X_DEVICE_ID', ''),
            "jpush_id": h.get('HTTP_X_JPUSH_ID', ''),
            "appid": h.get('HTTP_X_APPID', 0),
        }
        # logger.info(f"[GetHeaderInRequest] original headers: {headers}")
        if headers['timestamp'] is not None:
            headers['timestamp'] = Util.FormatInt(headers['timestamp'])

        if headers['userid'] is not None and headers['userid'] != '':
            headers['userid'] = int(headers['userid'])

        if headers['appid'] is not None and headers['appid'] != '':
            headers['appid'] = int(headers['appid'])

        if headers['lang'] is not None and headers['lang'] != '':
            if "-" in headers['lang']:
                headers['lang'] = headers['lang'].split("-")[0]
            else:
                headers['lang'] = 'en'
        # logger.info(f"[GetHeaderInRequest] final headers: {headers}")
        return headers

    def __verify_sign(self, request, headers):

        for x in ('token', 'timestamp', 'sign', 'device_id'):
            if headers[x] is None:
                logger.error(f"[VerifySign] url: {request.path}, param:{x} is missing")
                return False, ErrInfo.SIGN_PARAMETER_MISS

        device_id = headers['device_id']
        delay_seconds = abs(int(headers['timestamp'] / 1000000) - int(time.time()))
        if delay_seconds > settings.SIGN_TS_ALLOW_DIFF:
            logger.warning(f"[VerifySign] device_id:{device_id}, url: {request.path}, "
                           f"delay seconds:{delay_seconds} > {settings.SIGN_TS_ALLOW_DIFF}")
            return False, ErrInfo.SIGN_TIMESTAMP_EXPIRED

        # check if duplicated request
        is_dup_req = Util.GetFromCache(headers['sign'])
        if is_dup_req is not None:
            logger.error(f"[VerifySign] device_id:{device_id}, url: {request.path}, dup req, sign: {headers['sign']}")
            return False, ErrInfo.AUTH_DUPLICATE_REQUEST
        Util.Write2Cache(headers['sign'], 1, settings.SIGN_TS_ALLOW_DIFF)

        sign = Util.MD5Sum(
            str(headers['timestamp']) + headers['token'] + headers['device_id'] + settings.SIGN_SECRET_KEY)
        if sign == headers['sign']:
            logger.info(f"[VerifySign] device_id:{device_id}, url: {request.path}, check success")
            return True, 0

        logger.error(f"[VerifySign] device_id:{device_id}, url: {request.path}, sign invalid: {headers['sign']}")
        return False, ErrInfo.SIGN_INCORRECT

    @staticmethod
    def VerifySign(func):
        def wrapper(self, request):
            headers = self.GetHeaderInRequest(request)
            logger.info(f"[VerifySign] path:{request.path}, headers: {headers}, body:{request.body}")
            try:
                status, err_code = self.__verify_sign(request, headers)
            except Exception:
                logger.error(f"[VerifySign] failed path:{request.path}", exc_info=True)
                return self.ReturnError(ErrInfo.SERVER_ERROR)
            if status is not True:
                return self.ReturnError(err_code)
            return func(self, request)

        return wrapper

    def GetDataInGet(self, request, check_list=()):

        data = request.GET
        for item in check_list:
            if item not in data:
                logger.error(f"GetDataInGet missing param: {item}")
                return None, ErrInfo.JSON_REQUEST_PARA_MISS, "miss parameter: " + item

        return data, 0, None
