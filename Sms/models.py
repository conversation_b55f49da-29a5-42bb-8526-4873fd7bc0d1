from django.db import models


class Msg<PERSON>ana<PERSON>(models.Manager):
    pass


class SMS<PERSON><PERSON>ord(models.Model):
    device_id = models.CharField(max_length=64)
    latest_ts = models.BigIntegerField()
    direction = models.Char<PERSON>ield(max_length=20)  # in / out
    from_number = models.CharField(max_length=50)
    to_number = models.CharField(max_length=50)
    content = models.TextField(blank=True)
    images = models.CharField(max_length=2000, blank=True)  # if MMS
    link = models.CharField(max_length=2000, blank=True)  # ai 生成的链接
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_image = models.IntegerField(blank=True, default=0)  # 是否图片

    objects = MsgManager()
