from django.db import models


class Tool(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    objects = models.Manager()


class PushMessageTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Push a message'
        verbose_name_plural = 'Push a message'


class CustomerServiceTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'AI - Customer Service'
        verbose_name_plural = 'AI - Customer Service'
