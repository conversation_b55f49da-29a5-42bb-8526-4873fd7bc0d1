from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

# 1. 配置 Chrome 启动参数
chrome_options = Options()
# 关键参数：禁用沙箱（解决权限问题）
chrome_options.add_argument("--no-sandbox")
# 关键参数：禁用共享内存（避免内存占用冲突）
chrome_options.add_argument("--disable-dev-shm-usage")
# 可选：无头模式（不打开浏览器窗口，适合后台运行，若需可视化可删除）
chrome_options.add_argument("--headless=new")
# 可选：指定窗口大小（避免分辨率适配问题）
chrome_options.add_argument("--window-size=1920,1080")

# 2. 初始化 ChromeDriver（确保 chromedriver 路径正确或已配置环境变量）
# 若 chromedriver 在环境变量中，可简化为 service=Service()
service = Service(executable_path="C:/ChromeDriver/chromedriver.exe")  # 替换为你的驱动路径
driver = webdriver.Chrome(service=service, options=chrome_options)

# 测试：访问百度验证
driver.get("https://www.baidu.com")
print(driver.title)  # 输出“百度一下，你就知道”表示成功
driver.quit()
