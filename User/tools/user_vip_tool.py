from datetime import datetime, timed<PERSON><PERSON>
from typing import Union

from django.db import transaction

from Common.err import ErrInfo
from Common.timeutil import TimeUtil
from AiInsuranceBot import settings
from AiInsuranceBot.settings import logger
from Order.models import Order, OrderSendVip
from Order.tools.tool_invalid_order import OrderInvalidTool
from Order.tools.tool_order import OrderTool
from User.models import User


class UserVipTool:
    @staticmethod
    def get_send_vip_days(device_id: str) -> int:
        send_vip = OrderSendVip.objects.filter(device_id=device_id, deleted=0).first()
        if send_vip:
            return send_vip.send_days
        return 0

    @staticmethod
    def add_user_vip(device_id: str, send_days: int):
        before_sending = OrderSendVip.objects.filter(device_id=device_id).first()
        if not before_sending:
            send_vip = OrderSendVip(device_id=device_id, send_days=send_days)
            send_vip.save()
        else:
            before_sending.send_days += send_days
            before_sending.save()

    @staticmethod
    def refresh_user_vip(device_id: str) -> dict:
        order = Order.objects.filter(device_id=device_id).first()
        if not order:
            logger.warning(f"[CheckOrderStatus] device_id: {device_id}, order not exists!")
            return {"err": "invalid order, not exist"}

        cert_rsp = OrderInvalidTool.get_cert_rsp(order.device_id, order.certificate)
        if not cert_rsp:
            return {"err": "invalid order, no expired_at"}

        # 订单状态对不上、过期时间对不上、取消理由对不上则重新刷新
        ret = {
            "refresh": 0,
            "device_id": device_id,
        }

        if order.expiration_intent != int(cert_rsp.expiration_intent) \
                or order.order_status != cert_rsp.order_status \
                or abs(TimeUtil.GetDiffMinutes(order.expired_at, cert_rsp.expired_at)) >= 10:
            ret["refresh"] = 1
            ret["before_original_transaction_id"] = order.original_transaction_id
            ret["before_renew_status"] = order.renew_status
            ret["before_expiration_intent"] = order.expiration_intent
            ret["before_expired_at"] = order.expired_at
            ret["before_order_status"] = order.order_status

            order.original_transaction_id = cert_rsp.original_transaction_id
            order.renew_status = cert_rsp.renew_status
            order.expiration_intent = cert_rsp.expiration_intent
            order.expired_at = cert_rsp.expired_at
            order.order_status = cert_rsp.order_status
            order.save()

        # 更新用户的expire_at
        UserVipTool.update_user_all_expire(order.device_id, order.expired_at)

        ret.update({
            "original_transaction_id": cert_rsp.original_transaction_id,
            "renew_status": int(cert_rsp.renew_status),
            "expired_at": cert_rsp.expired_at,
            "order_status": cert_rsp.order_status,
            "expiration_intent": int(cert_rsp.expiration_intent),
            "order_valid": order.valid,
        })
        return ret

    @staticmethod
    def get_user_expired_at(device_id: str) -> Union[None, datetime]:
        if device_id in settings.WHITE_USER_LIST:
            logger.info(f"[UserVipTool.get_user_expired_at] user: {device_id} white user, vip.")
            return TimeUtil.GetNow() + timedelta(days=3)

        order = OrderTool.get_user_order(device_id)
        if order:
            return order.expired_at
        return None

    @staticmethod
    def is_user_vip_expire(device_id: str) -> ErrInfo:
        if device_id in settings.WHITE_USER_LIST:
            logger.info(f"[UserVipTool.is_user_vip_expire] user: {device_id} white user, vip.")
            return ErrInfo.SUCCESS

        # 这里不允许closed订单出现，不能豁免
        order = OrderTool.get_user_order(device_id)
        if not order:
            logger.info(f"[UserVipTool.is_user_vip_expire] user: {device_id} has no order, not vip.")
            return ErrInfo.JUMP_VIP_VIEW

        assert isinstance(order, Order)
        if TimeUtil.GetDiffMinutesWithNow(order.expired_at) < 1:
            logger.info(f"[UserVipTool.is_user_vip_expire] used: {device_id}, "
                        f"order expire at {order.expired_at}, now is expired, is not vip.")
            return ErrInfo.JUMP_VIP_VIEW

        return ErrInfo.SUCCESS

    @staticmethod
    def update_user_all_expire(device_id: str, expired_at: datetime):
        """
        更新所有生命周期的过期时间： order.order.expired_at + user.expired_at +  number_used.expired_at
        :param device_id: 用户ID
        :param expired_at: 订单实际的过期时间
        :return:
        """
        if not expired_at:
            logger.error(f"[UserVipTool.update_user_all_expire] update user {device_id} expire: {expired_at} is empty")
            return

        with transaction.atomic():
            logger.info(f"[UserVipTool.update_user_all_expire] update user {device_id} expire: {expired_at}")
            User.objects.filter(device_id=device_id).update(expired_at=expired_at)

    @staticmethod
    def add_user_vip_update_when_exists(device_id: str, send_days: int, update_days: int):
        record = OrderSendVip.objects.filter(device_id=device_id).first()
        if not record:
            send_vip = OrderSendVip(device_id=device_id, send_days=send_days)
            send_vip.save()
        else:
            record.send_days += update_days
            record.save()
