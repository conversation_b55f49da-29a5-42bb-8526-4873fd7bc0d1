# Generated by Django 3.2.5 on 2022-10-08 02:39

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Config',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('config', models.TextField(verbose_name='config')),
                ('channel', models.CharField(blank=True, max_length=50, verbose_name='channel')),
                ('version', models.IntegerField(blank=True, verbose_name='version')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
