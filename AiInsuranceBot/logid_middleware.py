import logging
import uuid

from contextvars import ContextVar

# 替换 threading.local 为 ContextVar
logid_context = ContextVar('logid', default='DEFAULT_LOGID')


class LogidFilter(logging.Filter):
    def filter(self, record):
        # 从 ContextVar 中获取 logid
        record.logid = logid_context.get()
        return True


class RequestLogMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 生成唯一 logid 并设置到 ContextVar 中
        logid = str(uuid.uuid4())
        logid_context.set(logid)
        request.logid = logid

        # 记录日志
        response = self.get_response(request)

        # 添加 logid 到响应头
        response['X-Logid'] = logid

        return response
