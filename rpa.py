# -*- coding: utf-8 -*-
import io
import json
import time
# from PIL import ImageGrab
from datetime import datetime

import pyautogui
import pyperclip
import win32clipboard
from PIL import Image
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from webdriver_manager.chrome import ChromeDriverManager


def jsz_image_upload(driver, args):
    element = driver.find_element(By.XPATH, "//img[@src='/sticsale/static/img/icon_10.056d12a.png']")
    element.click()
    time.sleep(0.5)

    # 获取隐藏的 input 元素
    file_input = driver.find_element(By.ID, "ocr-input-car")

    front_img_element = driver.find_element(By.CSS_SELECTOR, ".vux-flexbox-item .positive")
    front_img_element.click()  # 模拟点击触发 input
    time.sleep(0.2)  # 等待 JS 处理
    file_input.send_keys(args["jsz_image_path"])  # 上传文件
    time.sleep(0.5)  # 等待上传完成

    # 上传反面
    back_img_element = driver.find_element(By.CSS_SELECTOR, ".vux-flexbox-item .back")
    back_img_element.click()
    time.sleep(0.2)
    file_input.send_keys(args["jsz_image_path2"])
    time.sleep(0.5)

    pyautogui.press('esc')
    # time.sleep(0.2)
    # pyautogui.press('esc')
    # element = driver.find_element(By.XPATH, "//img[@src='/sticsale/static/img/xszzm.ad6c56e.png']")
    # element.click()
    # pyperclip.copy(args["jsz_image_path"])
    # time.sleep(1.5)
    # pyautogui.hotkey("command", "shift", "g")
    # time.sleep(0.2)
    # pyautogui.hotkey("command", "shift", "g")  # 打开“前往文件夹”
    # time.sleep(0.2)
    # pyautogui.hotkey("command", "v")  # 粘贴路径
    # time.sleep(0.4)
    # pyautogui.press("enter")
    # time.sleep(0.4)
    # pyautogui.press("enter")  # 确认选择文件
    # time.sleep(0.4)

    # pyperclip.copy("")
    # # <img src="/sticsale/static/img/xszfm.2034785.png" class="ocr-base-img">
    # element = driver.find_element(By.XPATH, "//img[@src='/sticsale/static/img/xszfm.2034785.png']")
    # element.click()
    # pyperclip.copy(args["jsz_image_path2"])
    # time.sleep(1)
    # pyautogui.hotkey("command", "shift", "g")   # 打开“前往文件夹”
    # time.sleep(0.5)
    # pyautogui.hotkey("command", "v")  # 粘贴路径
    # time.sleep(0.4)
    # pyautogui.press("enter")
    # time.sleep(0.4)
    # pyautogui.press("enter")
    # time.sleep(0.4)
    # click div text
    element = driver.find_element(By.XPATH, "//div[contains(text(),'识别')]")
    element.click()
    # time.sleep(5)


def base_info_click(driver, args):
    dj_date = args["djzy_date"]
    try:
        input_date = datetime.strptime(dj_date, "%Y-%m-%d").date()
        today = datetime.today().date()
        if input_date > today:
            return "❌ 转移登记日期不能大于今天"
    except ValueError as e:
        print(f"日期格式错误: {e}")
        return "❌ 日期格式错误，请使用 YYYY-MM-DD 格式"

    element = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//span[text()='转移登记日期']")
        )
    )
    # link.click()
    # element = driver.find_element(By.XPATH, "//span[text()='转移登记日期']")
    driver.execute_script("""arguments[0].classList.remove('vux-cell-placeholder');
    arguments[0].classList.add('vux-cell-value');arguments[0].textContent = arguments[1];
    arguments[0].dispatchEvent(new Event('change'));                      
    """, element, dj_date)
    time.sleep(0.1)
    element.click()
    time.sleep(0.1)
    element = driver.find_element(By.XPATH, "//div[contains(@class,'vux-datetime-confirm') and text()='完成']")
    element.click()
    time.sleep(0.1)

    if args.get("syx_gh", False):
        element = driver.find_element(By.XPATH, "//div[contains(text(),'商业过户')]")
        element.click()
        time.sleep(0.1)
    if args.get("jqx_gh", False):
        element = driver.find_element(By.XPATH, "//div[contains(text(),'交强过户')]")
        element.click()
        time.sleep(0.1)
    if args.get("is_white", False):
        element = driver.find_element(By.XPATH, "//div[contains(text(),'白名单')]")
        element.click()
        time.sleep(0.1)


def sfz_image_upload(driver, args):
    element = driver.find_element(By.CSS_SELECTOR, "img.go-back.car-list-img-right:not([style])")
    element.click()
    time.sleep(0.5)
    # 获取隐藏的 input 元素
    file_input = driver.find_element(By.ID, "ocr-input-per")

    front_img_element = driver.find_element(By.CSS_SELECTOR, ".vux-flexbox-item .positive")
    front_img_element.click()  # 模拟点击触发 input
    time.sleep(0.2)  # 等待 JS 处理
    file_input.send_keys(args["sfz_image_path"])  # 上传文件
    time.sleep(0.5)  # 等待上传完成

    # 上传反面
    back_img_element = driver.find_element(By.CSS_SELECTOR, ".vux-flexbox-item .back")
    back_img_element.click()
    time.sleep(0.2)
    file_input.send_keys(args["sfz_image_path2"])
    time.sleep(0.5)

    pyautogui.press('esc')
    # time.sleep(0.2)
    # pyautogui.press('esc')

    # click div text
    element = driver.find_element(By.XPATH, "//div[contains(text(),'识别')]")
    element.click()
    # time.sleep(3)

    input_box = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, '//input[@placeholder="联系电话"]')
        )
    )
    # input_box = driver.find_element(By.XPATH, '//input[@placeholder="联系电话"]')
    input_box.clear()
    input_box.send_keys(args["phone_number"])

    owner_email = driver.find_element(By.NAME, "tprptCarOwnerDto-email-input")
    driver.execute_script("""
        arguments[0].value = arguments[1];
        arguments[0].dispatchEvent(new Event('input'));
        arguments[0].dispatchEvent(new Event('change'));
    """, owner_email, "<EMAIL>")


def tbr_image_upload(driver, args):
    if not args.get("tbr_not_chezhu", False):
        return
    pyperclip.copy("")
    element = driver.find_element(By.XPATH,
                                  '//img[@class="go-back car-list-img-right" and contains(@style, "padding-left: 10px")]')
    element.click()
    time.sleep(0.5)

    # 获取隐藏的 input 元素
    file_input = driver.find_element(By.ID, "ocr-input-per")

    front_img_element = driver.find_element(By.CSS_SELECTOR, ".vux-flexbox-item .positive")
    front_img_element.click()  # 模拟点击触发 input
    time.sleep(0.2)  # 等待 JS 处理
    file_input.send_keys(args["tbr_sfz_image_path"])  # 上传文件
    time.sleep(0.5)  # 等待上传完成

    # 上传反面
    back_img_element = driver.find_element(By.CSS_SELECTOR, ".vux-flexbox-item .back")
    back_img_element.click()
    time.sleep(0.2)
    file_input.send_keys(args["tbr_sfz_image_path2"])
    time.sleep(0.5)

    pyautogui.press('esc')
    # time.sleep(0.2)
    # pyautogui.press('esc')
    element = driver.find_element(By.XPATH, "//div[contains(text(),'识别')]")
    element.click()
    # time.sleep(2)
    input_box = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.NAME, "tprptApplicantDto-email-input")
        )
    )
    # input_box = driver.find_element(By.NAME, "tprptApplicantDto-email-input")
    # input_box.clear()
    # input_box.send_keys("<EMAIL>")
    driver.execute_script("""
        arguments[0].value = arguments[1];
        arguments[0].dispatchEvent(new Event('input'));
        arguments[0].dispatchEvent(new Event('change'));
    """, input_box, "<EMAIL>")


def baoxian_info_click(driver, args):
    if args.get("not_syx", False):
        element = driver.find_element(By.XPATH,
                                      "//div[contains(@class,'car-offer-riskbtn') and normalize-space(text())='商业险']")
        element.click()
        time.sleep(0.1)
    else:
        elements = driver.find_elements(By.CSS_SELECTOR, "span.main-risk-name")
        if args.get("not_csx", False):
            target = None
            for el in elements:
                if el.text.strip() == "车损险":
                    target = el
                    break
            if target:
                driver.execute_script("arguments[0].click();", target)
            else:
                raise Exception("未找到车损险")

        if "csx_use" in args:
            input_box = driver.find_element(By.CSS_SELECTOR, "input.weui-input[placeholder='保额']")
            input_box.click()
            input_box.send_keys(Keys.COMMAND, "a")
            input_box.send_keys(Keys.DELETE)
            input_box.send_keys(args["csx_use"])
            time.sleep(0.1)
        if args.get("not_sy_szx", False):
            target = None
            for el in elements:
                if el.text.strip() == "商业三者险":
                    target = el
                    break
            if target:
                driver.execute_script("arguments[0].click();", target)
            else:
                raise Exception("未找到商业三者险")
            # time.sleep(0.1)

        div_elements = driver.find_elements(By.CSS_SELECTOR, "div.sele-offer-item")
        subNum = 0
        if div_elements[2].text.strip() != "0":
            subNum = 1
        if "sy_szx_use" in args:
            if len(div_elements) >= 10 - subNum:
                div_elements[9 - subNum].click()
            else:
                raise Exception("未找到商业三者险输入源")
            # click_div = driver.find_element(By.XPATH, "//div[contains(@class,'sele-offer-item')]//span[normalize-space(text())='250万']")
            # click_div.click()
            use_div = driver.find_element(By.XPATH,
                                          "//div[contains(text(),'{0}') and contains(@class,'item-sele-def')]".format(
                                              args["sy_szx_use"]))
            use_div.click()
            submit_div = driver.find_element(By.XPATH,
                                             "//div[contains(@class,'car-com-btm-right') and contains(normalize-space(),'确定')]")
            submit_div.click()

        if args.get("not_zwx_siji", False):
            target = None
            for el in elements:
                if el.text.strip() == "座位险(司机)":
                    target = el
                    break
            if target:
                driver.execute_script("arguments[0].click();", target)
            else:
                raise Exception("未找到座位险(司机)")
        if "zwx_siji_use" in args:
            if len(div_elements) >= 15 - subNum:
                div_elements[14 - subNum].click()
            else:
                raise Exception("未找到座位险(司机)输入源")
            use_div = driver.find_element(By.XPATH,
                                          "//div[contains(text(),'{0}') and contains(@class,'item-sele-def')]".format(
                                              args["zwx_siji_use"]))
            use_div.click()
            submit_div = driver.find_element(By.XPATH,
                                             "//div[contains(@class,'car-com-btm-right') and contains(normalize-space(),'确定')]")
            submit_div.click()
        if args.get("not_zwx_chengke", False):
            target = None
            for el in elements:
                if el.text.strip() == "座位险(乘客)":
                    target = el
                    break
            if target:
                driver.execute_script("arguments[0].click();", target)
            else:
                raise Exception("未找到座位险(乘客)")
        if "zwx_chengke_use" in args:
            if len(div_elements) >= 18 - subNum:
                div_elements[17 - subNum].click()
            else:
                raise Exception("未找到座位险(乘客)输入源")
            use_div = driver.find_element(By.XPATH,
                                          "//div[contains(text(),'{0}') and contains(@class,'item-sele-def')]".format(
                                              args["zwx_chengke_use"]))
            use_div.click()
            submit_div = driver.find_element(By.XPATH,
                                             "//div[contains(@class,'car-com-btm-right') and contains(normalize-space(),'确定')]")
            submit_div.click()

        if not args.get("not_sy_szx", False) and args.get("is_fujia_yiliao", False):
            target = None
            for el in elements:
                if el.text.strip() == "附加医保-三者":
                    target = el
                    break
            if target:
                driver.execute_script("arguments[0].click();", target)
            else:
                raise Exception("未找到附加医保-三者")
        if "fujia_yiliao_use" in args:
            if len(div_elements) >= 21 - subNum:
                div_elements[20 - subNum].click()
            else:
                raise Exception("未找到附加医保-三者输入源")
            use_div = driver.find_element(By.XPATH,
                                          "//div[contains(text(),'{0}') and contains(@class,'item-sele-def')]".format(
                                              args["fujia_yiliao_use"]))
            use_div.click()
            submit_div = driver.find_element(By.XPATH,
                                             "//div[contains(@class,'car-com-btm-right') and contains(normalize-space(),'确定')]")
            submit_div.click()

    if args.get("not_jqx", False):
        element = driver.find_element(By.XPATH,
                                      "//div[contains(@class,'car-offer-riskbtn') and normalize-space(text())='交强险']")
        element.click()
        time.sleep(0.1)

    submit = driver.find_element(By.XPATH,
                                 "//div[contains(@class,'car_rightbtn') and contains(normalize-space(),'保费计算')]")
    submit.click()
    # time.sleep(2)


def baoxian_info2_click(driver, args):
    element = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class,'task-companyName') and contains(normalize-space(),'泰康在线')]")
        )
    )
    # element = driver.find_element(By.XPATH, "//div[contains(@class,'task-companyName') and contains(normalize-space(),'泰康在线')]")
    element.click()
    time.sleep(0.1)
    toubaodi_element = driver.find_element(By.XPATH, "//span[normalize-space(text())='请选择']")
    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", toubaodi_element)
    toubaodi_element.click()
    input_box = driver.find_element(By.CSS_SELECTOR, "input.weui-input[placeholder='输入搜索']")
    input_box.click()
    input_box.send_keys(args["toubaodi"])
    input_box.send_keys(Keys.ENTER)

    select_elememt = driver.find_element(By.XPATH,
                                         "//div[contains(@class,'car-style') and contains(normalize-space(),'{0}')]".format(
                                             args["toubaodi"]))
    select_elememt.click()
    submit_div = driver.find_element(By.XPATH,
                                     "//div[contains(@class,'tabbar_item on') and contains(normalize-space(),'确定')]")
    submit_div.click()

    info_element = driver.find_element(By.XPATH,
                                       "//a[contains(@class,'com-sele--title') and contains(normalize-space(.),'点击获取交叉销售')]")
    info_element.click()

    link = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class,'sele-more-info') and contains(text(),'款-') and contains(text(),'元')]")
        )
    )
    link.click()
    time.sleep(0.1)
    if args.get("not_syx", False):
        # 只有交强
        click_ele = driver.find_element(By.XPATH, "//span[contains(text(),'E款-')]")
        click_ele.click()
    else:
        click_ele = driver.find_element(By.XPATH, "//span[contains(text(),'D款-')]")
        click_ele.click()

    confirm_list = driver.find_elements(By.XPATH, "//p[@class='label' and normalize-space(text())='确定']")
    confirm_list[len(confirm_list) - 1].click()

    compute_element = driver.find_element(By.XPATH,
                                          "//div[contains(@class,'vux-flexbox-item') and contains(normalize-space(),'保费计算')]")
    compute_element.click()
    pass


def share_click(driver, args):
    not_found = False
    try:
        element = driver.find_element(By.XPATH, "//button[contains(normalize-space(.), '分享报价')]")
        element.click()
    except:
        not_found = True
        pass
    # 尝试从其他地方分享报价
    if not_found:
        element = driver.find_element(By.XPATH, "//button[contains(normalize-space(.), '更多操作')]")
        element.click()
        element = driver.find_element(By.XPATH,
                                      "//div[contains(@class,'more-btn-item') and contains(normalize-space(.), '分享报价')]")
        element.click()

    link = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class,'share-item') and contains(text(),'报价单分享')]")
        )
    )
    link.click()
    time.sleep(1)
    share_link = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class,'carQuotation-close') and contains(text(),'复制报价单')]")
        )
    )
    share_link.click()
    time.sleep(1)

    win32clipboard.OpenClipboard()
    try:
        data = win32clipboard.GetClipboardData(win32clipboard.CF_DIB)
        if data:
            image = Image.open(io.BytesIO(data))
            image.save(args["share_name"], "PNG")
            print(f"✅ 已保存: {args['share_name']}")
        else:
            print("❌ 剪贴板没有图片")
    finally:
        win32clipboard.CloseClipboard()

    # image = ImageGrab.grabclipboard()  # 从剪贴板抓取
    # if image is None:
    #     raise Exception("未找到报价单")
    # image.save("/Users/<USER>/Downloads/" + "bjd.png", 'PNG')


def rpa_robot(url, args):
    user_data_dir = "/Users/<USER>/Downloads/chrome_profile"

    # 启动浏览器（这里用 Chrome，你需要先安装对应的 chromedriver）
    options = webdriver.ChromeOptions()
    # options.debugger_address = "127.0.0.1:9222"
    # options.add_argument("--start-maximized")  # 最大化窗口
    options.add_argument(f"--user-data-dir={user_data_dir}")  # 使用指定的用户数据目录
    options.add_argument("--profile-directory=Default")  # 使用默认用户配置

    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

    try:
        # 1. 打开网页
        # driver.new_window()
        driver.get(url)
        time.sleep(1)

        element = driver.find_element(By.XPATH, "//span[text()='首页']")
        element.click()
        time.sleep(0.2)

        element = driver.find_element(By.XPATH, "//div[@class='vux-tab-item' and contains(text(),'自主报价')]")
        element.click()
        time.sleep(0.2)

        jsz_image_upload(driver, args)

        base_info_click(driver, args)

        sfz_image_upload(driver, args)

        tbr_image_upload(driver, args)

        element = driver.find_element(By.XPATH,
                                      "//div[contains(@class,'car-btn-per') and contains(@class,'car-btn-div') and contains(text(),'同车主')]")
        # 滚动到可见位置
        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
        if args["bbr_with_chezhu"]:
            element.click()

        element = driver.find_element(By.XPATH, "//div[contains(text(),'下一步')]")
        element.click()
        time.sleep(0.5)
        # second_car = driver.find_element(
        #     By.XPATH,
        #     "(//div[contains(@class,'weui-cell') and contains(@class,'car-cell')])[2]"
        # )
        # second_car.click()
        # time.sleep(0.2)
        element = driver.find_element(By.XPATH, "//div[contains(text(),'下一步')]")
        element.click()
        time.sleep(0.5)
        # 定位输入框
        input_box = driver.find_element(By.ID, "vux-x-input-rji3g")

        # 清空内容
        input_box.clear()

        # 输入新内容，例如 "5"
        input_box.send_keys(args["site_number"])
        time.sleep(0.2)
        element = driver.find_element(By.XPATH, "//div[contains(text(),'下一步')]")
        element.click()
        time.sleep(0.5)

        baoxian_info_click(driver, args)

        baoxian_info2_click(driver, args)

        share_click(driver)

        print("✅ 提交完成！")
    except Exception as e:
        print("❌ 提交失败！")
        print(e)
    finally:
        driver.quit()


if __name__ == "__main__":
    json_str = """
    {"jsz_image_path": "/Users/<USER>/Downloads/jsz_test.jpg", 
    "jsz_image_path2": "/Users/<USER>/Downloads/jsz_test.jpg", 
    "djzy_date": "2024-08-21", "jqx_gh":true, "syx_gh":false, "is_white":false, 
    "sfz_image_path": "/Users/<USER>/Downloads/sfz_test1.jpg", 
    "sfz_image_path2": "/Users/<USER>/Downloads/sfz_test2.jpg", 
    "phone_number": "13418299452", 
    "tbr_not_chezhu":false, "bbr_with_chezhu":true, 
    "tbr_sfz_image_path": "/Users/<USER>/Downloads/other_sfz.jpg", 
    "tbr_sfz_image_path2": "/Users/<USER>/Downloads/other_sfz2.jpg",
    "site_number": "5",
    "not_syx": false,
    "not_jqx": false,
    "not_csx": false,
    "csx_use": "159700.00",
    "not_sy_szx": false,
    "sy_szx_use": "250万",
    "not_zwx_siji": true,
    "zwx_siji_use": "7万",
    "not_zwx_chengke": true,
    "zwx_chengke_use": "4x3万",
    "is_fujia_yiliao": true,
    "fujia_yiliao_use": "50万",
    "toubaodi": "东莞市",
    "share_name": "/Users/<USER>/Downloads/bjd.png"
    }
    """
    url = "https://m.ins10.cn/#/sticsale/agent?tabbar=true&icon=true"  # 目标网页
    args = json.loads(json_str)
    rpa_robot(url, args)
