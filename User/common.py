import time

from AiInsuranceBot.settings import logger
from Common.util import Util


class UserCommon:
    @staticmethod
    def CombineUserInfo(device_id: str) -> dict:
        j = {
            "device_id": device_id,
        }

        logger.info(f"[CombineUserInfo] device_id:{device_id}, info: {j}")
        return j

    @staticmethod
    def GenDeviceToken(device_id: str) -> str:
        return Util.MD5Sum(device_id + str(time.time()))
