"""
Django settings for AiInsuranceBot project.

Generated by 'django-admin startproject' using Django 2.1.5.

For more information on this file, see
https://docs.djangoproject.com/en/2.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.1/ref/settings/
"""
import logging
import os
import sys

import environ

env = environ.Env()
environ.Env.read_env()

LOCAL_DEV = True

DEBUG = LOCAL_DEV

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '2xgtu!=qpej@=^$11e=&-(c%=0c75^5jo8e*-zuiv_cw32+qyy'

# SECURITY WARNING: don't run with debug turned on in production!

ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'Config',
    'admin_totals',
    'Sms',
    'User',
    'ToolsForSms',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'AiInsuranceBot.logid_middleware.RequestLogMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'AiInsuranceBot.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')]
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'AiInsuranceBot.wsgi.application'

# --------------Database-----------------
# https://docs.djangoproject.com/en/2.1/ref/settings/#databases

DATABASES = {
    # 'default': {
    #     'ENGINE': 'django.db.backends.mysql',
    #     'NAME': env('DB_DATABASE_NAME_KEY'),
    #     'USER': env('DB_USER_KEY'),
    #     'PASSWORD': env('DB_PASSWORD_DEV_KEY'),
    #     'HOST': '127.0.0.1',
    #     'PORT': env('DB_PORT_KEY'),
    #     'OPTIONS': {'charset': 'utf8mb4'}
    # }

    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
    }
}

# if LOCAL_DEV is False:
#     DATABASES = {
#         'default': {
#             'ENGINE': 'django.db.backends.mysql',
#             'NAME': env('DB_DATABASE_NAME_KEY'),
#             'USER': env('DB_USER_KEY'),
#             'PASSWORD': env('DB_PASSWORD_KEY'),
#             'HOST': env('DB_HOST_KEY'),
#             'PORT': env('DB_PORT_KEY'),
#             'OPTIONS': {'charset': 'utf8mb4'},
#             "POOL_OPTIONS": {
#                 "POOL_SIZE": 15,
#                 "MAX_OVERFLOW": 5,
#                 "RECYCLE": 10 * 60  # 10 min,
#             },
#         }
#     }

# Password validation
# https://docs.djangoproject.com/en/2.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/2.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.1/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR + '/static'

## -----------sign & salt--------------------------------------------

SIGN_TS_ALLOW_DIFF = 3600 * 24  # 24 小时，防一手不同时区

# password salt
SALT_SECRET_KEY = 'Neu8HXM8Jvl9V7$ZZ$v3YHl98nT0U&3w5IoFT!K5ubi8'

# sign key
SIGN_SECRET_KEY = '!u3Nr&KKNZvwd&!xsAHy9(Npijed7!$0ZDcAD7Ep)IuV'

# max device number per account
MAX_DEVICE_NUM = 5

# ---------------------CACHE-------------------
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://127.0.0.1:6379/0",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
            "SERIALIZER": "django_redis.serializers.json.JSONSerializer"
        },
        "KEY_FUNCTION": "AiInsuranceBot.settings.make_key"
    }
}


def make_key(key, key_prefix, version):
    return '%s' % key


# Django 3.2+ 引入了DEFAULT_AUTO_FIELD设置，用于统一指定模型未显式定义主键时的默认类型
# BigAutoField是AutoField的扩展，支持更大的整数范围（适合数据量较大的项目）
# 设置后，所有未手动定义id主键的模型都会自动使用BigAutoField，从而消除这些警告( HINT: Configure the DEFAULT_AUTO_FIELD settings..)
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

DEFAULT_CACHE_TIMEOUT = 3600

FLAG_ADDRESS = 'https://127.0.0.1:8000/static/flag/'

# -------------- email ----------------

EMAIL_ACCOUNT = '<EMAIL>'
EMAIL_PASSWORD = 'xxxxx'
EMAIL_TEMPLATE = 'Hi there,<br><br>your password reset code is <b>%s</b><br><br>ZeHou Tech'


# ------------------- logging -------------------
class PathTruncatingFormatter(logging.Formatter):
    def format(self, record):
        if 'pathname' in record.__dict__.keys():
            record.pathname = record.pathname.replace(BASE_DIR, '')
        return super(PathTruncatingFormatter, self).format(record)


LOG_FORMAT = "[%(asctime)s][%(levelname)s][%(pathname)s:%(lineno)d] %(message)s"

ch = logging.StreamHandler(sys.stdout)
formatter = PathTruncatingFormatter(LOG_FORMAT)
root = logging.getLogger()
root.setLevel(logging.INFO)
ch.setFormatter(formatter)
root.addHandler(ch)

# ----------------encrypt------------------
AES_KEY = 'MkAdN-sd4ms-11LAqS-Mk0iUn-Bxs8ms'
AES_IV = '9gKs1R0T1sQtPx06'

# ---------------log--------------
LOG_DIR = os.path.join(BASE_DIR, "logs")
if not os.path.exists(LOG_DIR):
    os.mkdir(LOG_DIR)

##-------------- mail -------------
# 管理员邮箱

ADMINS = (
    ('fatpo', env('EMAIL_FATPO_KEY')),
)

TOTAL_ADMINS = (
    ('fatpo', env('EMAIL_FATPO_KEY')),
)

# 开发环境就不要邮件报警
if LOCAL_DEV:
    ADMINS = ()

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.qq.com'  # QQ邮箱SMTP服务器(邮箱需要开通SMTP服务)
EMAIL_PORT = 587  # QQ邮箱SMTP服务端口
EMAIL_HOST_USER = env('EMAIL_FROM_USER_KEY')  # 我的邮箱帐号
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD_KEY')  # 授权码  不是你的QQ密码
EMAIL_SUBJECT_PREFIX = '[GBJD]'  # 为邮件标题的前缀,默认是'[django]'
EMAIL_USE_TLS = True  # 开启安全链接
DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL_KEY')
SERVER_EMAIL = env('SERVER_EMAIL_KEY')  # 设置发件人

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '[%(levelname)s][%(asctime)s][%(logid)s][%(filename)s][%(funcName)s][%(lineno)d] > %(message)s' \
                .replace("\n", " ").replace("\r", " ")
        },
        'simple': {
            'format': '[%(levelname)s][%(logid)s] > %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
    },
    'handlers': {
        'console': {
            'level': 'WARNING',
            'filters': ['logid_filter'],
            'class': 'logging.StreamHandler',
            'formatter': 'standard'
        },
        'error_file_handler': {
            'level': 'WARNING',
            'filters': ['logid_filter'],
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': '%s/django.err' % LOG_DIR,
            'formatter': 'standard',
            'encoding': 'utf-8'
        },
        'file_handler': {
            'level': 'INFO',
            'filters': ['logid_filter'],
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': '%s/django.log' % LOG_DIR,
            'formatter': 'standard',
            'encoding': 'utf-8'
        },  # 用于文件输出
        'mail_admins_handler': {
            'level': 'ERROR',
            'filters': ['logid_filter'],
            'class': 'AiInsuranceBot.log_mail.MyEmailHandler',
            'formatter': 'standard',
            'include_html': True,
        },
    },
    'loggers': {
        'mdjango': {
            # 一个记录器中可以使用多个处理器
            'handlers': ['file_handler', 'mail_admins_handler', 'error_file_handler'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'django.request': {
            'handlers': ['mail_admins_handler'],
            'level': 'ERROR',
            'propagate': False,
        },
    },
    'filters': {
        'logid_filter': {
            '()': 'AiInsuranceBot.logid_middleware.LogidFilter',  # 指定你的过滤器
        },
    },
}

logger = logging.getLogger("mdjango")

# ORDER CONFIG
WHITE_USER_LIST = []

# URL
if LOCAL_DEV:
    STATIC_URL_BASE = 'http://127.0.0.1:8000'
else:
    STATIC_URL_BASE = 'http://127.0.0.1:8000'

# 客服号码
APP_IT_SUPPORT_SHOW_PHONE = env("APP_IT_SUPPORT_SHOW_PHONE")
APP_IT_SUPPORT_NO_NUMBER_PREFIX = env("APP_IT_SUPPORT_NO_NUMBER_PREFIX")
APP_IT_SUPPORT_DEVICEID = env("APP_IT_SUPPORT_DEVICEID")

# 短信相关
SMS_DIRECTION_SEND = "SEND"
SMS_DIRECTION_RECEIVE = "RECEIVE"

######################
SESSION_COOKIE_AGE = 3600 * 24 * 30  # admin登录 30 天
