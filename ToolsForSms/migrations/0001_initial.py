# Generated by Django 3.2.7 on 2025-09-12 07:17

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Tool',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='CustomerServiceTool',
            fields=[
            ],
            options={
                'verbose_name': 'AI - Customer Service',
                'verbose_name_plural': 'AI - Customer Service',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('ToolsForSms.tool',),
        ),
        migrations.CreateModel(
            name='PushMessageTool',
            fields=[
            ],
            options={
                'verbose_name': 'Push a message',
                'verbose_name_plural': 'Push a message',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('ToolsForSms.tool',),
        ),
    ]
