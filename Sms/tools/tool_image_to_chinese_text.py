import easyocr

reader = easyocr.Reader(['ch_sim', 'en'])  # 指定识别中文和英文


def get_text_from_image(image_path: str):
    result = reader.readtext(image_path)  # 替换为你的图片路径
    res = ""
    for detection in result:
        res += detection[1].replace(" ", "")
    return res


def classify_image_by_content(image_content: str):
    if "性别" in image_content:
        return "身份证正"
    if "民族" in image_content:
        return "身份证正"
    if "中华人民共和国" in image_content:
        return "身份证反"
    if "居民身份证" in image_content:
        return "身份证反"
    if "签发机关" in image_content:
        return "身份证反"
    if "公安局" in image_content:
        return "驾驶证"
    elif "行驶证" in image_content:
        return "驾驶证"
    elif "机动车" in image_content:
        return "驾驶证"
    elif "轿车" in image_content:
        return "驾驶证"
    elif "小型" in image_content:
        return "驾驶证"
    elif "型号" in image_content:
        return "驾驶证"
    elif "支队" in image_content:
        return "驾驶证"
    else:
        return "未知"


# 测试：用中文图片的Base64测试
if __name__ == "__main__":
    for image in ["image1.jpg", "image2.jpg", "image3.jpg"]:
        content = get_text_from_image(image)
        print(classify_image_by_content(content))
