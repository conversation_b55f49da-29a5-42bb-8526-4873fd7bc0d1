

class RedisKey:
    TOKEN_EXPIRE_TIMEOUT = 3600 * 24 * 1  # token 失效时间，秒
    SMS_LATEST_TIMESTAMP_EXPIRE_SECONDS = 24 * 3600 * 30  # 1个月

    @staticmethod
    def GenTokenKey(device_id: str) -> str:
        return '_'.join(['token', device_id])

    @staticmethod
    def gen_tmp_kill_user(device_id: str):
        return f"tmp_kill_user_{device_id}"

    @staticmethod
    def GenSmsLatestTimestamp(device_id: str):
        return f'sms:latest_ts:{device_id}'

    @staticmethod
    def gen_recent_ip_key(device_id: str, country: str) -> str:
        return f"recent_ip_{device_id}_{country}"
