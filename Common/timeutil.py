import datetime
import time
from typing import Union

import pytz

from AiInsuranceBot.settings import logger


class TimeUtil:

    @staticmethod
    def GetTodayDate() -> datetime:
        now = datetime.datetime.utcnow().replace(tzinfo=pytz.UTC)
        return now.date()

    @staticmethod
    def GetFutureFromNowByMinutes(minutes: int) -> datetime.datetime:
        dt = datetime.datetime.utcnow().replace(tzinfo=pytz.UTC)
        dt = dt + datetime.timedelta(minutes=minutes)
        return dt

    @staticmethod
    def GetFutureFromDtByMinutes(dt: datetime.datetime, minutes: int) -> datetime.datetime:
        dt = dt.replace(tzinfo=pytz.UTC)
        dt = dt + datetime.timedelta(minutes=minutes)
        return dt

    @staticmethod
    def time_str_to_ts(iso_time_str: str) -> int:
        """

        :param iso_time_str:   "2023-04-25T07:37:16.888+00:00"
        :return:
        """
        # 将ISO格式的时间字符串转换为datetime对象
        dt_obj = datetime.datetime.fromisoformat(iso_time_str)

        # 将datetime对象转换为Unix时间戳（秒数）
        timestamp_sec = int(dt_obj.timestamp())

        # 将Unix时间戳转换为13位时间戳（毫秒数）
        timestamp_ms = timestamp_sec * 1000
        return timestamp_ms

    @staticmethod
    def GetDateTimeBeforeNow(before_days: int) -> datetime.datetime:
        now = datetime.datetime.now(datetime.timezone.utc).replace(tzinfo=pytz.UTC)
        before_days_ago = (now - datetime.timedelta(days=before_days))
        return before_days_ago

    @staticmethod
    def GetDateTimeBeforeMinutes(before_minutes: int) -> datetime.datetime:
        now = datetime.datetime.now(datetime.timezone.utc).replace(tzinfo=pytz.UTC)
        before_minutes_ago = (now - datetime.timedelta(minutes=before_minutes))
        return before_minutes_ago

    @staticmethod
    def DateTime2Timestamp(dt: datetime.datetime):
        """
        13位毫秒
        :param dt:
        :return:
        """
        return int(datetime.datetime.timestamp(dt) * 1000)

    @staticmethod
    def GetNowTsInStr() -> str:
        """
        毫秒时间戳
        :return: 毫秒时间戳
        """
        return str(int(time.time() * 1000))

    @staticmethod
    def GetNowTsInInt() -> int:
        """
        毫秒时间戳
        :return: 毫秒时间戳
        """
        return int(time.time() * 1000)

    @staticmethod
    def TranDatetime2Timestamp(dt: datetime.datetime) -> int:
        """
        秒时间戳
        :return: 秒时间戳
        """
        return int(datetime.datetime.timestamp(dt))

    @staticmethod
    def TranDatetime2TimestampMs(dt: datetime.datetime) -> int:
        """
        毫秒时间戳
        :return: 毫秒时间戳
        """
        return int(datetime.datetime.timestamp(dt)) * 1000

    @staticmethod
    def GetBeforeDayTsInInt(days: int) -> int:
        """
        毫秒时间戳
        :return: 毫秒时间戳
        """
        return int(time.time() * 1000) - days * 24 * 3600 * 1000

    @staticmethod
    def GetBeforeMinTsInInt(minute: int) -> int:
        """
        毫秒时间戳
        :return: 毫秒时间戳
        """
        return int(time.time() * 1000) - minute * 60 * 1000

    @staticmethod
    def GetUTCDateTime(dt) -> Union[datetime.datetime, None]:
        if isinstance(dt, datetime.datetime):
            if dt.tzinfo != pytz.UTC:
                return dt.replace(tzinfo=pytz.UTC)
            return dt
        elif isinstance(dt, int):
            if len(str(dt)) == 10:
                return datetime.datetime.fromtimestamp(int(dt), tz=pytz.UTC)
            elif len(str(dt)) == 13:
                return datetime.datetime.fromtimestamp(int(dt) / 1000, tz=pytz.UTC)

        logger.error(f"[Common.util.GetUTCDateTime] sth is wrong for: {dt}")
        return None

    @staticmethod
    def AddTimeDays(dt, days: int) -> datetime.datetime:
        if days == 0:
            return dt

        before = dt
        if isinstance(dt, datetime.datetime):
            if dt.tzinfo != pytz.UTC:
                dt = dt.replace(tzinfo=pytz.UTC)

        dt = dt + datetime.timedelta(days=days)
        logger.info(f"[Common.util.AddTimeDays] before:{before}, after: {dt}, days: {days}")
        return dt

    @staticmethod
    def GetNow() -> datetime.datetime:
        dt = datetime.datetime.now(datetime.timezone.utc).replace(tzinfo=pytz.UTC)
        return dt

    @staticmethod
    def GetDiff(dt1: datetime.datetime, dt2: datetime.datetime) -> datetime.timedelta:
        dt1 = TimeUtil.GetUTCDateTime(dt1)
        dt2 = TimeUtil.GetUTCDateTime(dt2)
        return dt1 - dt2

    @staticmethod
    def GetDiffDays(dt1: datetime.datetime, dt2: datetime.datetime) -> int:
        dt1 = TimeUtil.GetUTCDateTime(dt1)
        dt2 = TimeUtil.GetUTCDateTime(dt2)
        return (dt1 - dt2).days

    @staticmethod
    def GetDiffDaysWithNow(dt1: datetime.datetime) -> int:
        return TimeUtil.GetDiffDays(dt1, TimeUtil.GetNow())

    @staticmethod
    def GetDiffHours(dt1: datetime.datetime, dt2: datetime.datetime) -> int:
        dt1 = TimeUtil.GetUTCDateTime(dt1)
        dt2 = TimeUtil.GetUTCDateTime(dt2)
        return int((dt1 - dt2).total_seconds() / 3600)

    @staticmethod
    def GetDiffHoursWithNow(dt1: datetime.datetime) -> int:
        return TimeUtil.GetDiffHours(dt1, TimeUtil.GetNow())

    @staticmethod
    def GetDiffMinutes(dt1: datetime.datetime, dt2: datetime.datetime) -> int:
        dt1 = TimeUtil.GetUTCDateTime(dt1)
        dt2 = TimeUtil.GetUTCDateTime(dt2)
        return int((dt1 - dt2).total_seconds() / 60)

    @staticmethod
    def GetDiffMinutesWithNow(dt1: datetime.datetime) -> int:
        return TimeUtil.GetDiffMinutes(dt1, TimeUtil.GetNow())

    @staticmethod
    def IsDt1BigThanDt2(dt1: datetime.datetime, dt2: datetime.datetime) -> bool:
        dt1 = TimeUtil.GetUTCDateTime(dt1)
        dt2 = TimeUtil.GetUTCDateTime(dt2)
        return (dt1 - dt2).total_seconds() > 0

    @staticmethod
    def IsDt1BigThanNow(dt1: datetime.datetime) -> bool:
        return TimeUtil.IsDt1BigThanDt2(dt1, TimeUtil.GetNow())

    @staticmethod
    def GetTodayDateStr() -> str:
        """
        毫秒时间戳
        :return: 毫秒时间戳
        """
        now = datetime.datetime.now(datetime.timezone.utc).replace(tzinfo=pytz.UTC)
        return now.strftime("%Y%m%d")

    @staticmethod
    def GetBeijingTimeStr(dt: datetime.datetime) -> str:
        if not dt:
            return ""
        t = datetime.datetime.fromtimestamp(TimeUtil.TranDatetime2Timestamp(dt),
                                            pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d %H:%M:%S')
        return t

    @staticmethod
    def GetBeijingDateStr(dt: datetime.datetime) -> str:
        t = datetime.datetime.fromtimestamp(TimeUtil.TranDatetime2Timestamp(dt),
                                            pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d')
        return t

    @staticmethod
    def GetBeijingTimeStrFromTs(ts: int) -> str:
        # 换成秒
        if len(str(ts)) == 13:
            ts = ts / 1000
        if len(str(ts)) == 16:
            ts = ts / 1000 / 1000

        t = datetime.datetime.fromtimestamp(ts, pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d %H:%M:%S')
        return t

    @staticmethod
    def apple_order_ts_to_dt(ts: int) -> datetime.datetime:
        return datetime.datetime.fromtimestamp(ts / 1000)

    @staticmethod
    def str_to_ds(time_str: str) -> datetime.datetime:
        date_object = datetime.datetime.strptime(time_str, "%Y-%m-%d")
        return date_object


if __name__ == '__main__':
    print(TimeUtil.GetDiffHours(TimeUtil.GetNow(), TimeUtil.GetNow() + datetime.timedelta(hours=12, days=2)))
    print(TimeUtil.GetBeijingTimeStr(TimeUtil.GetNow()))
    dt = datetime.datetime.now(datetime.timezone.utc).replace(tzinfo=pytz.UTC)
    print(TimeUtil.GetBeijingTimeStr(dt))
    print(TimeUtil.GetDateTimeBeforeMinutes(10))
