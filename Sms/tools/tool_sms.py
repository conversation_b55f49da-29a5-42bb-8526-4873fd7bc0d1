import datetime

from django.db.models import Q

from AiInsuranceBot import settings
from AiInsuranceBot.settings import logger
from Sms.models import SMSRecord
from User.tools.user_tool import UserTool


class SmsTool:
    @staticmethod
    def get_latest_sms_record(device_id: str, latest_ts: int) -> list:
        # <= before_ts 的短信都视为已删除
        # latest_ts为0时，拉用户的全部收和发的短信， 前提是[已删除]的不再拉取

        # latest_ts为0时，拉用户的全部收和发的短信
        if latest_ts == 0:
            records = SMSRecord.objects.filter(device_id=device_id, latest_ts__gt=latest_ts).all()
        else:
            # 否则只拉用户的比latest_ts大的收短信
            records = SMSRecord.objects.filter(device_id=device_id, latest_ts__gt=latest_ts,
                                               direction=settings.SMS_DIRECTION_RECEIVE).all()
        return records

    @staticmethod
    def get_userid_from_mock_number(number: str) -> int:
        t = number.replace("+999", "")
        # +9990123013
        if str(t).isdigit():
            return int(t)
        else:
            device_id_str = t
            u = UserTool.get_user_by_device_id(device_id_str)
            if u:
                return u.id
            else:
                logger.error(f"[SmsTool] get_userid_from_mock_number number:{number} not device: {t}")
                return -1

    @staticmethod
    def get_mock_number_by_user_id(user_id: int) -> str:
        length = len(str(user_id))
        zeros_to_add = 8 - length  # 计算需要添加的零的数量
        return f"+999{zeros_to_add * '0'}{user_id}"

    @staticmethod
    def get_mock_number_by_device_id(device_id: str) -> str:
        u = UserTool.get_user_by_device_id(device_id)
        if not u:
            logger.warning(f"[SmsTool] get_mock_number_by_device_id {device_id} not exists!")
            return ""
        return SmsTool.get_mock_number_by_user_id(u.id)

    @staticmethod
    def get_no_read_cnt(device_id: str, latest_ts: int) -> int:
        return SMSRecord.objects.filter(device_id=device_id, latest_ts__gt=latest_ts,
                                        direction=settings.SMS_DIRECTION_RECEIVE).count()

    @staticmethod
    def if_first_conversation(device_id: str, from_number: str, to_number: str) -> bool:
        if SMSRecord.objects.filter(device_id=device_id,
                                    from_number=from_number,
                                    to_number=to_number).exists():
            return False
        return True

    @staticmethod
    def get_records_by_customer_service(device_id: str, before: datetime.datetime) -> list:
        records = SMSRecord.objects.filter(device_id=device_id, created_at__gte=before).order_by("id").all()
        return records

    @staticmethod
    def get_device_id_from_mock_number(mock_phone: str) -> str:
        """
        从mock 的number抓出来 device_id
        :param mock_phone:
        :return:
        """
        return mock_phone  # 这个项目的 mock_phone 和 device_id 一样

    @staticmethod
    def get_it_by_both_number(device_id: str, number: str, before_date: datetime.datetime) -> list:
        query = Q(device_id=device_id, created_at__gte=before_date)
        query = query & (Q(from_number=number) | Q(to_number=number))

        records = SMSRecord.objects.filter(query).order_by("id").all()

        return records

    @staticmethod
    def get_latest_top_n_it_sms(device_id: str, n: int) -> list:
        records = SMSRecord.objects.filter(device_id=device_id, is_it=1).order_by('-id')[:n]
        return records

    @staticmethod
    def get_latest_top_n_non_it_sms(device_id: str, n: int) -> list:
        records = SMSRecord.objects.filter(device_id=device_id, is_it=0).order_by('-id')[:n]
        return records

    @staticmethod
    def get_conversation_cnt_both_direction(device_id: str, number: str) -> int:
        from_cnt = SMSRecord.objects.filter(device_id=device_id, from_number=number).count()
        to_cnt = SMSRecord.objects.filter(device_id=device_id, to_number=number).count()
        return from_cnt + to_cnt

    @staticmethod
    def get_conversation_sms(device_id: str, number: str) -> list:
        from_records = SMSRecord.objects.filter(device_id=device_id, from_number=number).all()
        to_records = SMSRecord.objects.filter(device_id=device_id, to_number=number).all()

        # 合并两个查询集并按 id 排序
        combined_records = from_records | to_records  # union() 会合并两个查询集
        combined_records = combined_records.order_by('id')  # 按照 id 排序

        return combined_records
