from django.core.cache import cache

from Common.rediskey import <PERSON><PERSON><PERSON>ey
from Order.models import BlackOrderRecord
from Order.tools.tool_order import OrderTool
from Order.tools.tool_refresh_order import RefreshOrderTool
from AiInsuranceBot import settings
from AiInsuranceBot.settings import logger
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool
from Sms.tools.tool_sms import SmsTool
from User.tools.user_vip_tool import UserVipTool


class UserKillTool:
    @staticmethod
    def tmp_kill_user(device_id: str):
        # 暂时封杀
        seconds = 3600
        cache.set(RedisKey.gen_tmp_kill_user(device_id), 1, seconds)
        logger.error(f"[UserKillTool] user tmp kill: {device_id} for {seconds} seconds")

    @staticmethod
    def is_tmp_kill(device_id: str) -> bool:
        key = RedisKey.gen_tmp_kill_user(device_id)
        val = cache.get(key)
        if val:
            ttl = cache.ttl(key)
            logger.warning(f"[UserKillTool] user tmp killed，{device_id}, ttl:{ttl} seconds")
            return True
        return False

    @staticmethod
    def un_kill_tmp_user(device_id: str):
        # 暂时解除封杀
        cache.delete(RedisKey.gen_tmp_kill_user(device_id))
        logger.error(f"[UserKillTool] un kill user: {device_id}")

    @staticmethod
    def kill_user(device_id: str, add_days: int):
        if not device_id:
            logger.error(f"[UserKillTool] device_id is nil")
            return

        if add_days >= 0:
            logger.error(f"[UserKillTool] device_id:{device_id}, add_days:{add_days} invalid")
            return

        # 加VIP
        UserVipTool.add_user_vip(device_id, add_days)

        # 更新 expired
        vip_ret = RefreshOrderTool.refresh_user_vip(device_id)
        logger.error(f"[封号]{vip_ret}")

        # 记录下
        try:
            order = OrderTool.get_user_order_without_condition(device_id)
            black_order_record = BlackOrderRecord(device_id=device_id,
                                                  original_transaction_id=order.original_transaction_id,
                                                  ban_days=add_days, cert_md5=order.cert_md5)
            black_order_record.save()
        except Exception:
            logger.error(f"[UserKillTool] device_id:{device_id}, add_days:{add_days} record failed", exc_info=True)

        # 推送通知
        push_content = SmsNoticeTool.temporary_ban_notification()
        to_number = SmsTool.get_mock_number_by_device_id(device_id)
        SmsItSupportTool.add_support_sms(device_id, settings.SMS_DIRECTION_RECEIVE,
                                         settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)

        return
