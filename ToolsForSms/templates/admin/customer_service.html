{# templates/admin/customer_service.html #}
{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
<style>
    /* 按钮悬停效果 */
    .floating-button:hover {
        background-color: #0056b3 !important;
        transform: translateY(-1px);
        transition: all 0.2s ease;
    }

    .send-button:hover {
        background-color: #218838 !important;
        transform: translateY(-1px);
        transition: all 0.2s ease;
    }

    .send-button:disabled {
        background-color: #6c757d !important;
        cursor: not-allowed !important;
        transform: none !important;
    }

    /* 按钮容器样式 */
    .button-container {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;
        align-items: center;
    }
</style>


<div class="container mt-4">
    <h1>Customer Service</h1>
    <div style="display: flex;">
        <!-- 左侧列表 -->
        <div style="flex: 1; border: 1px solid #ccc; padding: 10px; margin-right: 10px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); background-color: #f9f9f9;">
            <h2 style="text-align: center; font-family: Arial, sans-serif; color: #333;">Conversation List</h2>
            <ul id="conversation-list"
                style="list-style-type: none; padding: 0; margin: 0;  max-height: 70vh;  overflow-y: auto;">
                {% for message in conversation_list %}
                <li style="cursor: pointer; padding: 10px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 6px; background-color: #fff; transition: box-shadow 0.3s ease;"
                    onclick="loadDetail('{{ message.number }}', this)">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <span style="margin-right: 10px; font-weight: bold; color: #555;">{{ forloop.counter }}.</span>
                            <span style="color: #333;">{{ message.number }}</span>
                        </div>
                        <div style="color: #aaa; font-size: 1.2em;">
                            {{ message.time }}
                        </div>
                    </div>
                    <div style="color: #777; font-size: 0.9em; margin-top: 5px;">
                        {{ message.message }}
                    </div>
                </li>
                {% endfor %}
            </ul>
        </div>
        <!-- 右侧详细信息 -->
        <div id="detail" style="flex: 2; border: 1px solid #ccc; padding: 10px;">
            <h2><span id="detail-title">Details</span></h2>
            <hr/>
            <br>

            <div id="conversation-container" style="overflow-y: auto; max-height: 60vh;">
                <!-- 会话消息会被插入到这里 -->
            </div>

            <!-- 发送消息区域 -->
            <div id="message-input-area" style="margin-top: 20px;">
                <div class="floating-window-container" style="max-height: 150px; overflow-y: auto;">
                    <div id="floatingWindow" class="floating-window"
                         style=" border: 1px solid #ccc; display: none">
                        {{ quick_reply_html|safe }}
                    </div>
                </div>

                <!-- 快捷回复和发送按钮区域 -->
                <div class="button-container">
                    <button class="floating-button" onclick="toggleFloatingWindow()"
                            style="padding: 8px 16px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                        Quick Reply
                    </button>
                    <button id="send-button" class="send-button" onclick="sendMessage()"
                            style="padding: 8px 16px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: 500;">
                        <span class="send-button-text">发送消息</span>
                    </button>
                </div>

                <label for="message-content"></label>
                <textarea id="message-content" rows="6" style="width: 100%; height: 150px;" data-id=""></textarea>

                <!-- 证件图片上传区域 -->
                <div style="margin-top: 15px; padding: 12px; background-color: #f8f9fa; border-radius: 6px; border: 1px solid #e9ecef;">
                    <h4 style="margin-top: 0; margin-bottom: 10px; color: #495057; font-size: 16px;">证件图片上传</h4>

                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 10px;">
                        <div>
                            <label for="id_card_front"
                                   style="display: block; margin-bottom: 3px; font-weight: 500; font-size: 13px;">身份证正面:</label>
                            <input type="file" id="id_card_front" accept="image/*"
                                   onchange="previewImage('id_card_front_preview', this.files[0])"
                                   style="padding: 3px; border: 1px solid #ced4da; border-radius: 3px; font-size: 12px; width: 100%;">
                            <div id="id_card_front_preview"
                                 style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px; min-height: 20px;"></div>
                        </div>

                        <div>
                            <label for="id_card_back"
                                   style="display: block; margin-bottom: 3px; font-weight: 500; font-size: 13px;">身份证反面:</label>
                            <input type="file" id="id_card_back" accept="image/*"
                                   onchange="previewImage('id_card_back_preview', this.files[0])"
                                   style="padding: 3px; border: 1px solid #ced4da; border-radius: 3px; font-size: 12px; width: 100%;">
                            <div id="id_card_back_preview"
                                 style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px; min-height: 20px;"></div>
                        </div>

                        <div>
                            <label for="driver_license_front"
                                   style="display: block; margin-bottom: 3px; font-weight: 500; font-size: 13px;">驾驶证正面（或正副页）:</label>
                            <input type="file" id="driver_license_front" accept="image/*"
                                   onchange="previewImage('driver_license_front_preview', this.files[0])"
                                   style="padding: 3px; border: 1px solid #ced4da; border-radius: 3px; font-size: 12px; width: 100%;">
                            <div id="driver_license_front_preview"
                                 style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px; min-height: 20px;"></div>
                        </div>

                        <div>
                            <label for="driver_license_back"
                                   style="display: block; margin-bottom: 3px; font-weight: 500; font-size: 13px;">驾驶证副面（可选）:</label>
                            <input type="file" id="driver_license_back" accept="image/*"
                                   onchange="previewImage('driver_license_back_preview', this.files[0])"
                                   style="padding: 3px; border: 1px solid #ced4da; border-radius: 3px; font-size: 12px; width: 100%;">
                            <div id="driver_license_back_preview"
                                 style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px; min-height: 20px;"></div>
                        </div>
                    </div>
                </div>


            </div>

        </div>
    </div>
</div>

<!-- 图片放大模态框 -->
<div id="imageModal"
     style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8); cursor: pointer;"
     onclick="closeImageModal()">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); max-width: 90%; max-height: 90%;">
        <img id="modalImage"
             style="max-width: 100%; max-height: 100%; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.5);">
        <div style="position: absolute; top: 10px; right: 10px; color: white; font-size: 30px; font-weight: bold; cursor: pointer; background: rgba(0,0,0,0.5); width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;"
             onclick="closeImageModal()">&times;
        </div>
    </div>
</div>

<script>
    const codeDict = {{ code_json|safe }};

    // 图片放大功能
    function openImageModal(imageSrc) {
        const modal = document.getElementById('imageModal');
        const modalImage = document.getElementById('modalImage');
        modalImage.src = imageSrc;
        modal.style.display = 'block';

        // 阻止事件冒泡
        event.stopPropagation();
    }

    function closeImageModal() {
        const modal = document.getElementById('imageModal');
        modal.style.display = 'none';
    }

    // 按ESC键关闭模态框
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeImageModal();
        }
    });

    function toggleFloatingWindow() {
        const floatingWindow = document.getElementById('floatingWindow');
        if (floatingWindow.style.display === 'block') {
            floatingWindow.style.display = 'none';
        } else {
            floatingWindow.style.display = 'block';
        }
    }

    function fillInputNew(dataId) {
        const messageInput = document.getElementById('message-content');
        messageInput.value = codeDict[dataId];
        toggleFloatingWindow(); // 选中快捷回复后自动关闭浮窗
    }


    function loadDetail(number, element) {
        // 移除之前选中的高亮样式
        const listItems = document.querySelectorAll('#conversation-list li');
        listItems.forEach(item => {
            item.style.backgroundColor = '#fff'; // 清空内联背景色
            item.style.border = '1px solid #ddd'; // 清空内联边框
        });

        // 添加当前选中的高亮样式
        element.style.backgroundColor = 'rgb(250 250 210)'; // 设置内联背景色
        element.style.border = '2px solid rgb(240 222 230)'; // 设置内联边框

        const url = "{% url 'admin:customer_service_detail' 'NUMBER' %}".replace('NUMBER', number);
        fetch(url)
            .then(response => response.json())
            .then(data => {
                console.log('Received data:', data); // 调试信息

                const detailTitleDiv = document.getElementById('detail-title');
                detailTitleDiv.innerHTML = '<h2> Details: &nbsp;&nbsp;&nbsp;' + number + '</h2>'; // Clear previous content

                const containerDiv = document.getElementById('conversation-container');
                containerDiv.innerHTML = ''; // Clear previous content

                // 检查是否是错误响应
                if (data.err_code && data.err_code !== 0) {
                    console.error('Server error:', data.err_msg);
                    containerDiv.innerHTML = `<div style="padding: 20px; text-align: center; color: #d32f2f;">Error: ${data.err_msg}</div>`;
                    return;
                }

                // 检查数据结构
                if (!data || !data.details) {
                    console.error('Invalid data structure:', data);
                    containerDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No conversation data available or invalid data format.</div>';
                    return;
                }

                if (!Array.isArray(data.details)) {
                    console.error('data.details is not an array:', data.details);
                    containerDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Invalid conversation data format.</div>';
                    return;
                }

                if (data.details.length === 0) {
                    containerDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No messages found for this conversation.</div>';
                    return;
                }

                data.details.forEach(item => {
                    const messageDiv = document.createElement('div');
                    let content = item.content ? item.content.replace(/\n/g, '<br>').replace(/\\n/g, '<br>') : '';

                    // 处理图片URLs（新增的image_urls字段）
                    let imageContent = '';
                    if (item.image_urls && item.image_urls.length > 0) {
                        imageContent = item.image_urls.map(imageUrl => {
                            return `<img src="${imageUrl}" style="max-width: 120px; max-height: 120px; margin: 3px; border-radius: 6px; cursor: pointer; transition: transform 0.2s ease; display: inline-block;"
                                       onclick="openImageModal('${imageUrl}')"
                                       onmouseenter="this.style.transform='scale(1.05)'"
                                       onmouseleave="this.style.transform='scale(1)'"
                                       onerror="this.style.display='none'">`;
                        }).join('');
                    }

                    // 如果没有image_urls字段，回退到检查content中的图片URL（向后兼容）
                    if (!imageContent && content) {
                        const imageUrlPattern = /(https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp))/gi;
                        const isImageUrl = imageUrlPattern.test(content);

                        if (isImageUrl) {
                            const imageUrl = content.match(imageUrlPattern)[0];
                            imageContent = `<img src="${imageUrl}" style="max-width: 120px; max-height: 120px; border-radius: 6px; cursor: pointer; transition: transform 0.2s ease;"
                                           onclick="openImageModal('${imageUrl}')"
                                           onmouseenter="this.style.transform='scale(1.05)'"
                                           onmouseleave="this.style.transform='scale(1)'"
                                           onerror="this.style.display='none'">`;
                            content = ''; // 清空content，因为已经作为图片显示了
                        }
                    }

                    // 合并文字内容和图片内容
                    const finalContent = (content ? content : '') + (imageContent ? (content ? '<br>' : '') + imageContent : '');

                    if (item.is_right === true) {
                        messageDiv.style.textAlign = 'right';
                        messageDiv.innerHTML = `<div style="clear: both;"></div>
                    <div style="display: inline-block; text-align: left; background: #d1e7dd; border: 1px solid #ccc; padding: 10px; margin: 5px; border-radius: 10px; float: right; max-width: 70%;">
                        ${finalContent}
                    </div>
                    <div style="clear: both;"></div>`;

                    } else {
                        messageDiv.style.textAlign = 'left';
                        messageDiv.innerHTML = `<div style="clear: both;"></div>
                    <div style="display: inline-block; text-align: left; background: #f8d7da; border: 1px solid #ccc; padding: 10px; margin: 5px; border-radius: 10px; max-width: 70%;">
                        ${finalContent}
                    </div>
                    <div style="clear: both;"></div>`;
                    }
                    containerDiv.appendChild(messageDiv);
                });

                // 自动滚动到最新消息
                setTimeout(() => {
                    containerDiv.scrollTop = containerDiv.scrollHeight;
                }, 0);

                // 将 number 存储在 textarea 和按钮的 data-id 属性中
                document.getElementById('message-content').setAttribute('data-id', number);
                document.getElementById('send-button').setAttribute('data-id', number);

                if (data.details && data.details.length > 0) {
                    // 更新最后消息时间戳
                    const latestMessage = data.details.reduce((latest, current) => {
                        const latestTs = latest.latest_ts;
                        const currentTs = current.latest_ts;
                        return currentTs > latestTs ? current : latest;
                    }, data.details[0]);

                    lastMessageTimestamp = latestMessage.latest_ts;
                    console.log('after load details, Last message timestamp:', lastMessageTimestamp);
                }
            })
            .catch(error => {
                console.error('Error fetching details:', error);
                const containerDiv = document.getElementById('conversation-container');
                containerDiv.innerHTML = `<div style="padding: 20px; text-align: center; color: #d32f2f;">
                    Network Error: Failed to load conversation details.<br>
                    <small style="color: #666;">Please check your connection and try again.</small>
                </div>`;
            });
    }

    function sendMessage() {
        const number = document.getElementById('message-content').getAttribute('data-id');

        // 获取三个独立的图片文件
        const idCardFrontFile = document.getElementById('id_card_front').files[0];
        const idCardBackFile = document.getElementById('id_card_back').files[0];
        const driverLicenseFrontFile = document.getElementById('driver_license_front').files[0];
        const driverLicenseBackFile = document.getElementById('driver_license_back').files[0];

        const messageContent = document.getElementById('message-content').value;

        // 检查是否输入了消息内容或图片
        if (!messageContent && !idCardFrontFile && !idCardBackFile && !driverLicenseFrontFile) {
            alert('请输入消息内容或上传图片');
            return;
        }

        // 检查三张必需的图片是否都已上传（如果用户开始上传图片，则必须完整上传三张）
        if (idCardFrontFile || idCardBackFile || driverLicenseFrontFile) {
            if (!idCardFrontFile) {
                alert('请上传身份证正面图片');
                return;
            }

            if (!idCardBackFile) {
                alert('请上传身份证反面图片');
                return;
            }

            if (!driverLicenseFrontFile) {
                alert('请上传驾驶证图片');
                return;
            }
        }

        // 获取发送按钮并暂时禁用，防止重复点击
        const sendButton = document.getElementById('send-button');
        const originalText = sendButton.innerHTML;
        sendButton.disabled = true;
        sendButton.innerHTML = '<span class="send-button-text">发送中...</span>';

        const url = "{% url 'admin:customer_service_send_msg' %}";
        const formData = new FormData();
        formData.append('number', number);
        formData.append('content', messageContent);

        lastMessageTimestamp = new Date().getTime();
        formData.append('latest_ts', lastMessageTimestamp);
        formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');

        // 添加图片（如果存在）
        if (idCardFrontFile) formData.append('images', idCardFrontFile);
        if (idCardBackFile) formData.append('images', idCardBackFile);
        if (driverLicenseFrontFile) formData.append('images', driverLicenseFrontFile);
        if (driverLicenseBackFile) formData.append('images', driverLicenseBackFile);

        fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        }).then(response => response.json())
            .then(data => {
                // 恢复按钮状态
                sendButton.disabled = false;
                sendButton.innerHTML = originalText;

                if (data.err_code !== 0) {
                    alert('消息发送失败: ' + data.err_msg);
                    return;
                }

                const containerDiv = document.getElementById('conversation-container');

                // 自己发的文字
                if (messageContent) {
                    const messageDiv = document.createElement('div');
                    let content = messageContent.replace(/\n/g, '<br>');
                    messageDiv.style.textAlign = 'right';
                    messageDiv.innerHTML = `<div style="clear: both;"></div>
                        <div style="display: inline-block; text-align: left; background: #d1e7dd; border: 1px solid #ccc; padding: 10px; margin: 5px; border-radius: 10px; float: right; max-width: 70%;">
                            ${content}                    </div>
                        <div style="clear: both;"></div>`;
                    containerDiv.appendChild(messageDiv);
                }

                // 自己发的图片（本地预览）
                const imgFiles = [idCardFrontFile, idCardBackFile, driverLicenseFrontFile, driverLicenseBackFile].filter(file => file != null);
                if (imgFiles.length > 0) {
                    const imgDiv = document.createElement("div");
                    imgDiv.style.textAlign = "right";
                    imgFiles.forEach(file => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const img = document.createElement("img");
                            img.src = e.target.result;  // 直接显示本地 DataURL
                            img.style.maxWidth = "120px";
                            img.style.maxHeight = "120px";
                            img.style.margin = "5px";
                            img.style.borderRadius = "6px";
                            img.style.cursor = "pointer";
                            img.style.transition = "transform 0.2s ease";

                            // 添加悬停效果
                            img.onmouseenter = function() {
                                this.style.transform = "scale(1.05)";
                            };
                            img.onmouseleave = function() {
                                this.style.transform = "scale(1)";
                            };

                            // 添加点击放大功能
                            img.onclick = function() {
                                openImageModal(e.target.result);
                            };

                            imgDiv.appendChild(img);

                            // 保证滚动条跟随到底部
                            setTimeout(() => {
                                containerDiv.scrollTop = containerDiv.scrollHeight;
                            }, 0);
                        };
                        reader.readAsDataURL(file);
                    });
                    containerDiv.appendChild(imgDiv);
                }

                // 清空输入框和图片选择
<!--                document.getElementById('message-content').value = '';-->
<!--                document.getElementById('id_card_front').value = '';-->
<!--                document.getElementById('id_card_back').value = '';-->
<!--                document.getElementById('driver_license').value = '';-->
<!--                document.getElementById('id_card_front_preview').innerHTML = '';-->
<!--                document.getElementById('id_card_back_preview').innerHTML = '';-->
<!--                document.getElementById('driver_license_front_preview').innerHTML = '';-->
<!--                document.getElementById('driver_license_back_preview').innerHTML = '';-->

                // 重置轮询计数器
                pollingCount = 0;
                // 启动轮询, 查看是否有新消息
                startPolling(number);

                setTimeout(() => {
                    containerDiv.scrollTop = containerDiv.scrollHeight;
                }, 0);
            })
            .catch(error => {
                console.error('Error sending message:', error);
                alert('发送消息失败，请重试');
                // 恢复按钮状态
                sendButton.disabled = false;
                sendButton.innerHTML = originalText;
            });
    }


    function toggleMenu(id) {
        const menu = document.getElementById(id);
        if (menu.classList.contains('show')) {
            menu.classList.remove('show');
        } else {
            menu.classList.add('show');
        }
    }

    // 按ESC键关闭模态框
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeImageModal();
        }
    });

    // 更新预览函数，添加缩略图和文件名显示功能
    function previewImage(previewElementId, file) {
        const preview = document.getElementById(previewElementId);
        preview.innerHTML = ''; // 清空上一次的预览

        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const container = document.createElement('div');
                container.style.display = 'inline-flex';
                container.style.flexDirection = 'column';
                container.style.alignItems = 'center';
                container.style.margin = '5px';
                container.style.maxWidth = '110px';

                const img = document.createElement('img');
                img.src = e.target.result;
                img.style.maxWidth = '100px';
                img.style.maxHeight = '100px';
                img.style.border = '1px solid #ccc';
                img.style.borderRadius = '6px';
                img.style.cursor = 'pointer';
                img.style.transition = 'transform 0.2s ease';
                img.style.objectFit = 'cover';
                img.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';

                // 添加悬停效果
                img.onmouseenter = function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.boxShadow = '0 4px 10px rgba(0,0,0,0.2)';
                };
                img.onmouseleave = function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
                };

                // 添加点击放大功能
                img.onclick = function() {
                    openImageModal(e.target.result, file.name);
                };

                // 添加文件名显示
                const fileName = document.createElement('div');
                fileName.textContent = file.name.length > 15 ? file.name.substring(0, 15) + '...' : file.name;
                fileName.style.fontSize = '11px';
                fileName.style.marginTop = '5px';
                fileName.style.textAlign = 'center';
                fileName.style.color = '#666';
                fileName.style.wordBreak = 'break-all';
                fileName.style.maxWidth = '100px';
                fileName.style.overflow = 'hidden';
                fileName.style.textOverflow = 'ellipsis';

                container.appendChild(img);
                container.appendChild(fileName);
                preview.appendChild(container);
            };
            reader.readAsDataURL(file);
        }
    }

    let pollingInterval = null;
    let lastMessageTimestamp = 0;
    const maxPollingCount = 80; // 最大轮询次数
    let currentChatNumber = null;

    function startPolling(number) {
            // 停止之前的轮询
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            currentChatNumber = number;
            pollingCount = 0; // 重置计数器

            // 开始新的轮询，每秒检查一次
            pollingInterval = setInterval(() => {
            if (pollingCount >= maxPollingCount) {
                stopPolling();
                showPollingTimeoutNotification();
                return;
            }

            checkForNewMessages(number);
            pollingCount++;
        }, 1000);
    }

    function stopPolling() {
        if (pollingInterval) {
            clearInterval(pollingInterval);
            pollingInterval = null;
        }
    }

    function showPollingTimeoutNotification() {
        const pollingTimeoutSeconds = maxPollingCount;
        const message = `已经轮询了 ${pollingTimeoutSeconds} 秒，暂时无法获取最新消息。\n\n可能的原因：\n• 网络连接不稳定\n• 服务器响应较慢\n• 当前没有新消息\n\n是否要刷新页面重新开始？`;

        if (confirm(message)) {
            // 用户选择刷新页面
            window.location.reload();
        } else {
            // 用户选择不刷新，可以手动重新开始轮询
            console.log('用户选择不刷新页面，轮询已停止');
        }
    }


    function checkForNewMessages(number) {
        console.log('Checking for new messages... ' , number, lastMessageTimestamp);

        if (!number || !lastMessageTimestamp) return;

        const url = "{% url 'admin:check_new_messages' %}";
        fetch(`${url}?number=${encodeURIComponent(number)}&latest_ts=${lastMessageTimestamp}`)
            .then(response => response.json())
            .then(data => {
                if (data.err_code === 0 && data.has_new) {
                     // 停止轮训
                    stopPolling();

                    // 有新消息，重新加载对话详情
                    loadDetail(number, document.querySelector(`#conversation-list li[onclick*="'${number}'"]`));
                }
            })
            .catch(error => {
                console.error('检查新消息失败:', error);
            });
    }


    // 页面卸载时停止轮询
    window.addEventListener('beforeunload', () => {
        stopPolling();
    });

</script>
{% endblock %}
