{# templates/admin/push_a_message.html #}
{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h1>Push a Message</h1>
    <button id="ban-scam-text-btn" type="button" class="btn ban-text-btn mt-3">加载违规</button>

    <form id="custom-form" method="post" action="{% url 'admin:push_message' %}">
        {% csrf_token %}
        <div class="form-row">
            <div class="form-group col-md-6">
                <label for="deviceid">Device Id:</label>
                <input type="text" id="deviceid" name="deviceid">
            </div>
            <br>
            <div class="form-group col-md-6">
                <label for="push_content">Push Content:</label>
                <textarea type="text" id="push_content" name="push_content" rows="10" cols="50"></textarea>
            </div>

            <div class="form-group col-md-6">
                <label for="push_image">Push image:</label>
                <textarea type="text" id="push_image" name="push_image" rows="3" cols="50"></textarea>
            </div>
            <br>
            <div>
                <span>JJ: a2055d47-a449-485e-ab44-78aa57ede8b4</span>
            </div>
            <br>
            <div>
                <span>Fatpo: df5a230c-cfe9-4842-ad75-80b501851b89</span>
            </div>
        </div>
        <button type="submit">Submit</button>

    </form>
    <div class="form-group mt-3">
        <label for="response-textbox">Response</label>
        <textarea id="response-textbox" class="form-control" readonly rows="20" cols="60"></textarea>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 自动填充 "违规通知"
        document.getElementById('ban-text-btn').addEventListener('click', function () {
            const contentTextarea = document.getElementById('push_content');
            contentTextarea.value = `您好，\n\n我们遗憾地通知您，您的账户因涉嫌违规使用，已违反《应用使用条例》第XXX条规定（具体内容：禁止利用本平台从事违法违规活动及损害他人合法权益的行为）。\n\n根据条例相关约定，我们将对您的账户采取限制措施。如您对处理结果有异议，可在收到通知后7个工作日内联系客服进行申诉。\n\n感谢您的理解与配合。`;
        });
    });

    document.getElementById('custom-form').onsubmit = function (event) {
        event.preventDefault();
        var formData = new FormData(this);
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        }).then(response => response.json()).then(data => {
            var responseTextbox = document.getElementById('response-textbox');
            responseTextbox.value = JSON.stringify(data, null, 2);
        });
    };
</script>
{% endblock %}
