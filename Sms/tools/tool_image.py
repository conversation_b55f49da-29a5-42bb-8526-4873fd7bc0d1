import base64
import os
import random
import string
import time
from datetime import datetime

from AiInsuranceBot import settings
from AiInsuranceBot.settings import logger


class ImageUtil:

    @staticmethod
    def gen_random_filename():
        now = int(time.time())
        random_str = ''.join(random.sample(string.ascii_letters + string.digits, 8))
        return str(now) + '_' + random_str

    @staticmethod
    def save_image_with_base64_file_data(file_data, file_format):
        try:
            today = datetime.today().strftime("%Y-%m-%d")
            file_dir = settings.BASE_DIR + "/%s/uploads/%s" % ('static', today)
            if not os.path.exists(file_dir):
                os.makedirs(file_dir)

            filename = "%s.%s" % (ImageUtil.gen_random_filename(), file_format)
            filename_abs = "%s/%s" % (file_dir, filename)
            logger.info(f"[SendSms] image: {filename_abs}")

            f = open(filename_abs, 'wb')
            f.write(base64.b64decode(file_data))
            f.close()

            filename_base = "/uploads/%s/%s" % (today, filename)
            return filename_abs, settings.STATIC_URL_BASE + filename_base
        except Exception:
            logger.error('[SendSms] error: ', exc_info=True)
            return None

    @staticmethod
    def save_image(file_data, file_format):
        try:
            # 1. 获取静态文件根目录（Windows 下是 C:\xxx\ai-insurance-bot\static）
            static_root = settings.STATICFILES_DIRS[0]

            # 2. 拼接图片保存目录：static_root → uploads → 2025-09-29（全用 os.path.join）
            today = datetime.today().strftime("%Y-%m-%d")
            file_dir = os.path.join(static_root, 'uploads', today)  # 自动生成 C:\xxx\static\uploads\2025-09-29

            # 3. 创建目录（exist_ok=True 避免重复创建报错）
            os.makedirs(file_dir, exist_ok=True)

            # 4. 生成随机文件名（保留你的 gen_random_filename 方法）
            random_name = ImageUtil.gen_random_filename()
            filename = f"{random_name}.{file_format}"  # 如 1759132723_ObCpc7r5.png

            # 5. 拼接完整保存路径（Windows 下是 C:\xxx\static\uploads\2025-09-29\xxx.png）
            filename_abs = os.path.join(file_dir, filename)
            logger.info(f"[SendSms] 图片保存路径: {filename_abs}")  # 此时日志会显示全 \ 的路径，无混合

            # 6. 写入文件（确保 file_data 完整）
            with open(filename_abs, 'wb') as f:
                bytes_written = f.write(file_data)  # 新增：记录实际写入字节数
            logger.info(f"[SendSms] 实际写入字节数: {bytes_written}")  # 验证是否和 file_data 长度一致

            # 7. 生成可访问的 URL（URL 用 /，不影响）
            # 拼接 URL 路径：/static/uploads/2025-09-29/xxx.png
            filename_url = os.path.join('uploads', today, filename).replace('\\', '/')  # 把 \ 换成 URL 用的 /
            image_url = f"http://43.139.183.108{settings.STATIC_URL}{filename_url}"

            return filename_abs, image_url
        except Exception:
            logger.error('[SendSms] error: ', exc_info=True)
            return None
