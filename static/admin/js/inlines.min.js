var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.findInternal=function(b,d,a){b instanceof String&&(b=String(b));for(var c=b.length,f=0;f<c;f++){var g=b[f];if(d.call(a,g,f,b))return{i:f,v:g}}return{i:-1,v:void 0}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(b,d,a){b!=Array.prototype&&b!=Object.prototype&&(b[d]=a.value)};
$jscomp.getGlobal=function(b){return"undefined"!=typeof window&&window===b?b:"undefined"!=typeof global&&null!=global?global:b};$jscomp.global=$jscomp.getGlobal(this);$jscomp.polyfill=function(b,d,a,c){if(d){a=$jscomp.global;b=b.split(".");for(c=0;c<b.length-1;c++){var f=b[c];f in a||(a[f]={});a=a[f]}b=b[b.length-1];c=a[b];d=d(c);d!=c&&null!=d&&$jscomp.defineProperty(a,b,{configurable:!0,writable:!0,value:d})}};
$jscomp.polyfill("Array.prototype.find",function(b){return b?b:function(b,a){return $jscomp.findInternal(this,b,a).v}},"es6","es3");
(function(b){b.fn.formset=function(d){var a=b.extend({},b.fn.formset.defaults,d),c=b(this);d=c.parent();var f=function(a,e,d){var c=new RegExp("("+e+"-(\\d+|__prefix__))");e=e+"-"+d;b(a).prop("for")&&b(a).prop("for",b(a).prop("for").replace(c,e));a.id&&(a.id=a.id.replace(c,e));a.name&&(a.name=a.name.replace(c,e))},g=b("#id_"+a.prefix+"-TOTAL_FORMS").prop("autocomplete","off"),h=parseInt(g.val(),10),e=b("#id_"+a.prefix+"-MAX_NUM_FORMS").prop("autocomplete","off"),m=""===e.val()||0<e.val()-g.val();
c.each(function(e){b(this).not("."+a.emptyCssClass).addClass(a.formCssClass)});if(c.length&&m){var l=a.addButton;null===l&&("TR"===c.prop("tagName")?(c=this.eq(-1).children().length,d.append('<tr class="'+a.addCssClass+'"><td colspan="'+c+'"><a href="#">'+a.addText+"</a></tr>"),l=d.find("tr:last a")):(c.filter(":last").after('<div class="'+a.addCssClass+'"><a href="#">'+a.addText+"</a></div>"),l=c.filter(":last").next().find("a")));l.on("click",function(d){d.preventDefault();d=b("#"+a.prefix+"-empty");
var c=d.clone(!0);c.removeClass(a.emptyCssClass).addClass(a.formCssClass).attr("id",a.prefix+"-"+h);c.is("tr")?c.children(":last").append('<div><a class="'+a.deleteCssClass+'" href="#">'+a.deleteText+"</a></div>"):c.is("ul")||c.is("ol")?c.append('<li><a class="'+a.deleteCssClass+'" href="#">'+a.deleteText+"</a></li>"):c.children(":first").append('<span><a class="'+a.deleteCssClass+'" href="#">'+a.deleteText+"</a></span>");c.find("*").each(function(){f(this,a.prefix,g.val())});c.insertBefore(b(d));
b(g).val(parseInt(g.val(),10)+1);h+=1;""!==e.val()&&0>=e.val()-g.val()&&l.parent().hide();c.find("a."+a.deleteCssClass).on("click",function(d){d.preventDefault();c.remove();--h;a.removed&&a.removed(c);b(document).trigger("formset:removed",[c,a.prefix]);d=b("."+a.formCssClass);b("#id_"+a.prefix+"-TOTAL_FORMS").val(d.length);(""===e.val()||0<e.val()-d.length)&&l.parent().show();var m,g=function(){f(this,a.prefix,k)};var k=0;for(m=d.length;k<m;k++)f(b(d).get(k),a.prefix,k),b(d.get(k)).find("*").each(g)});
a.added&&a.added(c);b(document).trigger("formset:added",[c,a.prefix])})}return this};b.fn.formset.defaults={prefix:"form",addText:"add another",deleteText:"remove",addCssClass:"add-row",deleteCssClass:"delete-row",emptyCssClass:"empty-row",formCssClass:"dynamic-form",added:null,removed:null,addButton:null};b.fn.tabularFormset=function(d,a){var c=b(this),f=function(a){b(d).not(".add-row").removeClass("row1 row2").filter(":even").addClass("row1").end().filter(":odd").addClass("row2")},g=function(){"undefined"!==
typeof SelectFilter&&(b(".selectfilter").each(function(b,a){b=a.name.split("-");SelectFilter.init(a.id,b[b.length-1],!1)}),b(".selectfilterstacked").each(function(b,a){b=a.name.split("-");SelectFilter.init(a.id,b[b.length-1],!0)}))},h=function(a){a.find(".prepopulated_field").each(function(){var c=b(this).find("input, select, textarea"),d=c.data("dependency_list")||[],e=[];b.each(d,function(b,c){e.push("#"+a.find(".field-"+c).find("input, select, textarea").attr("id"))});e.length&&c.prepopulate(e,
c.attr("maxlength"))})};c.formset({prefix:a.prefix,addText:a.addText,formCssClass:"dynamic-"+a.prefix,deleteCssClass:"inline-deletelink",deleteText:a.deleteText,emptyCssClass:"empty-form",removed:f,added:function(a){h(a);"undefined"!==typeof DateTimeShortcuts&&(b(".datetimeshortcuts").remove(),DateTimeShortcuts.init());g();f(a)},addButton:a.addButton});return c};b.fn.stackedFormset=function(d,a){var c=b(this),f=function(a){b(d).find(".inline_label").each(function(a){a+=1;b(this).html(b(this).html().replace(/(#\d+)/g,
"#"+a))})},g=function(){"undefined"!==typeof SelectFilter&&(b(".selectfilter").each(function(a,b){a=b.name.split("-");SelectFilter.init(b.id,a[a.length-1],!1)}),b(".selectfilterstacked").each(function(a,b){a=b.name.split("-");SelectFilter.init(b.id,a[a.length-1],!0)}))},h=function(a){a.find(".prepopulated_field").each(function(){var c=b(this).find("input, select, textarea"),d=c.data("dependency_list")||[],e=[];b.each(d,function(b,c){e.push("#"+a.find(".form-row .field-"+c).find("input, select, textarea").attr("id"))});
e.length&&c.prepopulate(e,c.attr("maxlength"))})};c.formset({prefix:a.prefix,addText:a.addText,formCssClass:"dynamic-"+a.prefix,deleteCssClass:"inline-deletelink",deleteText:a.deleteText,emptyCssClass:"empty-form",removed:f,added:function(a){h(a);"undefined"!==typeof DateTimeShortcuts&&(b(".datetimeshortcuts").remove(),DateTimeShortcuts.init());g();f(a)},addButton:a.addButton});return c};b(document).ready(function(){b(".js-inline-admin-formset").each(function(){var d=b(this).data(),a=d.inlineFormset;
switch(d.inlineType){case "stacked":d=a.name+"-group .inline-related";b(d).stackedFormset(d,a.options);break;case "tabular":d=a.name+"-group .tabular.inline-related tbody:first > tr",b(d).tabularFormset(d,a.options)}})})})(django.jQuery);
