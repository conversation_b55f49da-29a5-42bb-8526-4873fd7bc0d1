import base64
import datetime
import json
import re

from django.contrib import admin
from django.http import JsonResponse
from django.template.response import TemplateResponse
from django.urls import path

from AiInsuranceBot import settings
from AiInsuranceBot.settings import logger
from Common.timeutil import TimeUtil
from Sms.tools.tool_image import ImageUtil
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_sms import SmsTool
from ToolsForSms.models import PushMessageTool, CustomerServiceTool
from User.tools.user_context_tool import UserContextTool


@admin.register(PushMessageTool)
class PushMessageToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/push_message.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('push_message/', self.admin_site.admin_view(self.push_message), name='push_message'),
        ]
        return custom_urls + urls

    @staticmethod
    def extract_urls(text):
        # 正则表达式模式，用于匹配 URL
        pattern = r'https?://\S+'
        # 使用 findall 方法找到所有匹配的 URL
        urls = re.findall(pattern, text)
        return urls

    def push_message(self, request):
        if request.method == 'POST':
            device_id = request.POST.get('deviceid').strip()
            content = request.POST.get('push_content').strip()
            image_url = request.POST.get('push_image').strip()

            to_number = SmsTool.get_mock_number_by_device_id(device_id)
            logger.info(f"[PushMessageToolAdmin] device_id:{device_id}, to_number:{to_number}, content:{content}, "
                        f"image:{image_url}")

            if image_url:
                SmsItSupportTool.add_support_mms_both_from_it(device_id, to_number, image_url)
                link = image_url
            else:
                links = self.extract_urls(content)
                if links:
                    link = links[0]
                else:
                    link = ""
                SmsItSupportTool.add_support_sms_both_from_it_with_link(device_id, to_number, content, link=link)

            response_data = {"device_id": device_id, "content": content, "image": image_url, "link": link}
            return JsonResponse(response_data)

        return TemplateResponse(request, 'admin/push_message.html', {})


@admin.register(CustomerServiceTool)
class CustomerServiceToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/customer_service.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('customer_service_detail/<str:number>/', self.admin_site.admin_view(self.customer_service_detail),
                 name='customer_service_detail'),
            path('customer_service_send_msg/', self.admin_site.admin_view(self.customer_service_send_msg),
                 name='customer_service_send_msg'),
        ]
        return custom_urls + urls

    def changelist_view(self, request, extra_context=None):
        try:
            # 这里加载你想要在初始化时加载的数据
            extra_context = CustomerServiceToolAdmin.get_conversations()

            reply_data_path = settings.BASE_DIR + '/Config/replylist.json'
            try:
                with open(reply_data_path) as f:
                    reply_data = json.loads(f.read())
            except Exception:
                logger.error(f"[CustomerServiceToolAdmin] failed to load replyList: {reply_data_path}")
                reply_data = []

            code_dict = {}
            item_counter = 0

            def generate_menu(items):
                nonlocal item_counter
                html = "<ul>"
                for item in items:
                    indent = item['level'] * 20
                    if "children" in item:
                        html += (f'<li onclick="toggleMenu(\'{item["label"]}-menu\')" style="margin-left: {indent}px;">'
                                 f'{item["label"]}')
                        html += f'<ul id="{item["label"]}-menu" class="nested-menu">'
                        html += generate_menu(item["children"])
                        html += '</ul></li>'
                    elif "code" in item:
                        data_id = f'item-{item_counter}'
                        code_dict[data_id] = item["code"]
                        html += (f'<li onclick="fillInputNew(\'{data_id}\')" style="margin-left: {indent}px;" '
                                 f'data-id="{data_id}">{item["label"]}</li>')
                        item_counter += 1
                html += "</ul>"
                return html

            quick_reply_html = generate_menu(reply_data)
            extra_context["quick_reply_html"] = quick_reply_html
            extra_context["code_json"] = json.dumps(code_dict)
            return super().changelist_view(request, extra_context=extra_context)
        except Exception as e:
            logger.error(f"[CustomerServiceToolAdmin] error: {e}", exc_info=True)
            return TemplateResponse(request, 'admin/customer_service.html', {})

    def customer_service_detail(self, request, number):
        try:
            logger.info(f"{request.META}")
            details = self.get_conversation_details(number)
            return JsonResponse({"details": details})
        except Exception as e:
            logger.error(f"[CustomerServiceToolAdmin] error: {e}", exc_info=True)
            return JsonResponse({"err_code": -1, "err_msg": "failed: " + str(e)})

    def customer_service_send_msg(self, request):
        if request.method == 'POST':
            data = request.POST

            to_number = data["number"]
            content = data["content"]
            images = request.FILES.getlist("images")  # 获取上传的图片文件

            # 检查是否有内容或图片
            if not content and not images:
                return JsonResponse({"err_code": -1, "err_msg": "Please enter a message or upload an image"})

            to_device_id = SmsTool.get_device_id_from_mock_number(to_number)

            if not to_device_id:
                logger.error(f"[CustomerServiceToolAdmin] failed device_id not exists, to_number:{to_number}")
                return JsonResponse({"err_code": -1, "err_msg": "device_id invalid"})

            # 处理图片消息
            image_urls = []
            if images:
                for image_file in images:
                    try:
                        # 读取图片文件内容
                        image_data = image_file.read()
                        # 转换为base64
                        image_base64 = base64.b64encode(image_data).decode('utf-8')
                        # 获取文件扩展名
                        file_format = image_file.name.split('.')[-1].lower() if '.' in image_file.name else 'jpg'

                        # 保存图片并获取URL
                        image_url = ImageUtil.save_image(image_base64, file_format)
                        if image_url:
                            image_urls.append(image_url)
                        else:
                            logger.error(f"[CustomerServiceToolAdmin] failed to save image: {image_file.name}")
                            return JsonResponse({"err_code": -1, "err_msg": f"Failed to save image: {image_file.name}"})

                    except Exception as e:
                        logger.error(f"[CustomerServiceToolAdmin] error processing image {image_file.name}: {e}",
                                     exc_info=True)

            # 存储消息
            SmsItSupportTool.add_support_sms_mms_both_from_it(to_device_id, to_number, content,
                                                              json.dumps(image_urls))

        return JsonResponse({"err_code": 0, "err_msg": "success"})

    @staticmethod
    def get_conversation_details(number) -> list:
        device_id = SmsTool.get_device_id_from_mock_number(number)

        logger.info(f"[CustomerServiceToolAdmin] device_id:{device_id}, number: {number}")

        # 构建查询条件
        before_date = datetime.datetime.now() - datetime.timedelta(days=90)
        records = SmsTool.get_it_by_both_number(device_id, number, before_date)
        logger.info(f"[CustomerServiceToolAdmin] device_id:{device_id}, size:{len(records)}")
        for index, i in enumerate(records):
            logger.info(f"[CustomerServiceToolAdmin] index:{index}, {i.from_number}:{i.to_number}:{i.content}")

        res = []
        for r in records:
            content = r.content
            if r.from_number == settings.APP_IT_SUPPORT_SHOW_PHONE:
                is_right = True
            else:
                is_right = False

            d = {
                "is_right": is_right,
                "from_number": r.from_number,
                "to_number": r.to_number,
                "created_at": r.created_at,
                "direction": r.direction,
                "content": f"[{TimeUtil.GetBeijingTimeStr(r.created_at)}]\n{content if content else r.images}"
            }

            res.append(d)

        return res

    @staticmethod
    def get_conversations() -> dict:
        # 最近的对话
        logger.info(f"[CustomerServiceToolAdmin] start...")
        before_date = datetime.datetime.now() - datetime.timedelta(days=30)
        records = SmsTool.get_records_by_customer_service(settings.APP_IT_SUPPORT_DEVICEID, before_date)
        group_records = {}
        logger.info(f"[CustomerServiceToolAdmin] get_conversations before size: {len(records)}")

        for r in records:
            if r.from_number == settings.APP_IT_SUPPORT_SHOW_PHONE:
                number = r.to_number
            else:
                number = r.from_number

            if number not in group_records:
                group_records[number] = [r]
            else:
                group_records[number].append(r)

        # 自己的号码就不用看了
        if settings.APP_IT_SUPPORT_SHOW_PHONE in group_records:
            del group_records[settings.APP_IT_SUPPORT_SHOW_PHONE]

        tt = {}
        un_read_size = 0
        for from_number, _records in group_records.items():
            logger.info(f"[CustomerServiceToolAdmin] {from_number}, size: {len(_records)}")

            count_rs_after_last_s = 0
            last_r_ts = -1
            last_content = ""

            # 排除掉纯系统通知的group
            user_send_cnt = len([v for v in _records if v.direction == settings.SMS_DIRECTION_RECEIVE])
            if user_send_cnt == 0:
                continue

            for item in reversed(_records):
                if item.direction == settings.SMS_DIRECTION_RECEIVE:
                    last_r_ts = max(item.latest_ts, last_r_ts)
                    count_rs_after_last_s += 1
                    if not last_content:
                        last_content = item.content
                elif item.direction == settings.SMS_DIRECTION_SEND:
                    if last_r_ts < 0:
                        last_r_ts = item.latest_ts
                    if not last_content:
                        last_content = "[客服回复] " + item.content
                    break

            # 计算未读数量
            if count_rs_after_last_s > 0:
                un_read_size += 1

            tt[from_number] = {
                "no_read_cnt": count_rs_after_last_s,
                "latest_ts": last_r_ts,
                "last_content": last_content,
            }
        logger.info(f"[CustomerServiceToolAdmin] beforeConversationSize: {len(group_records)}, "
                    f"afterConversationSize: {len(tt)},  noReadSize: {un_read_size}")
        sorted_res = dict(sorted(tt.items(), key=CustomerServiceToolAdmin.custom_sort))

        messages = []
        for k, v in sorted_res.items():
            messages.append({
                "head": k[0:3],
                "number": k,
                "time": TimeUtil.GetBeijingTimeStrFromTs(v['latest_ts']),
                "message": v["last_content"],
                "tips": v["no_read_cnt"] if "no_read_cnt" in v else 0
            })

        context = {
            "conversation_start_from": TimeUtil.GetBeijingTimeStr(before_date),
            "conversation_list": messages,
            "conversation_size": len(tt),
            "conversation_unread_size": un_read_size,
        }
        return context

    @staticmethod
    def custom_sort(item):
        # 根据 "no_read_cnt" 的值来排序，no_read_cnt 大的排在前面
        if item[1]["no_read_cnt"] > 0:
            return 0, -item[1]["latest_ts"]
        else:
            return 1, -item[1]["latest_ts"]

    @staticmethod
    def get_user_ctx(to_device_id: str, to_number: str) -> str:
        logger.info(f"[CustomerServiceToolAdmin] backend door get context: {to_device_id}:{to_number}")
        user_profile_ctx_str = UserContextTool.get_user_context_str("", "", to_device_id)
        return user_profile_ctx_str

    @staticmethod
    def get_user_it_sms_list(to_device_id: str, to_number: str) -> str:
        logger.info(f"[CustomerServiceToolAdmin] backend door get sms list: {to_device_id}:{to_number}")
        records = SmsTool.get_latest_top_n_it_sms(to_device_id, 50)

        res = []
        for v in records:
            time_ts = v.created_at.strftime('%Y-%m-%d %H:%M:%S')
            _content = v.content if v.content else v.images
            res.append(f"[{time_ts}][{v.from_number}]->[{v.to_number}]:{_content}")
        if len(res) == 0:
            res.append("该用户没有短信!")
        return json.dumps(res, indent=4, default=str, ensure_ascii=False)

    @staticmethod
    def get_user_non_it_sms_list(to_device_id: str, to_number: str) -> str:
        logger.info(f"[CustomerServiceToolAdmin] backend door get sms list: {to_device_id}:{to_number}")
        records = SmsTool.get_latest_top_n_non_it_sms(to_device_id, 50)

        res = []
        for v in records:
            time_ts = v.created_at.strftime('%Y-%m-%d %H:%M:%S')
            _content = v.content if v.content else v.images
            res.append(f"[{time_ts}][{v.from_number}]->[{v.to_number}]:{_content}")
        if len(res) == 0:
            res.append("该用户没有短信!")
        return json.dumps(res, indent=4, default=str, ensure_ascii=False)
