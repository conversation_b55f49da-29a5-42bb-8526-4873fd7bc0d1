/* Django 3.2.25 官方 nav_sidebar.css */
#nav-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 240px;
    z-index: 1000;
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
    transform: translateX(-100%);
    transition: transform 0.2s ease-in-out;
}

#nav-sidebar.toggled {
    transform: translateX(0);
}

#nav-sidebar .module {
    padding: 0;
    margin-bottom: 1rem;
}

#nav-sidebar .module h2 {
    padding: 0.75rem 1rem;
    margin: 0;
    font-size: 1rem;
    background: #e9ecef;
    border-bottom: 1px solid #dee2e6;
}

#nav-sidebar .module ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

#nav-sidebar .module li {
    margin: 0;
}

#nav-sidebar .module a {
    display: block;
    padding: 0.5rem 1rem;
    color: #343a40;
    text-decoration: none;
    border-bottom: 1px solid #dee2e6;
}

#nav-sidebar .module a:hover,
#nav-sidebar .module a.active {
    background: #0d6efd;
    color: white;
}

#toggle-nav-sidebar {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1010;
}

.admin-main {
    margin-left: 0;
    transition: margin-left 0.2s ease-in-out;
}

.admin-main.toggled {
    margin-left: 240px;
}

@media (min-width: 992px) {
    #nav-sidebar {
        transform: translateX(0);
    }
    .admin-main {
        margin-left: 240px;
    }
    #toggle-nav-sidebar {
        display: none;
    }
}