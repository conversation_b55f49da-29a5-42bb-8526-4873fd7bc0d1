{# templates/admin/customer_service.html #}
{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h1>Customer Service</h1>
    <div style="display: flex;">
        <!-- 左侧列表 -->
        <div style="flex: 1; border: 1px solid #ccc; padding: 10px; margin-right: 10px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); background-color: #f9f9f9;">
            <h2 style="text-align: center; font-family: Arial, sans-serif; color: #333;">Conversation List</h2>
            <ul id="conversation-list"
                style="list-style-type: none; padding: 0; margin: 0;  max-height: 70vh;  overflow-y: auto;">
                {% for message in conversation_list %}
                <li style="cursor: pointer; padding: 10px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 6px; background-color: #fff; transition: box-shadow 0.3s ease;"
                    onclick="loadDetail('{{ message.number }}', this)">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <span style="margin-right: 10px; font-weight: bold; color: #555;">{{ forloop.counter }}.</span>
                            <span style="color: #333;">{{ message.number }}</span>
                        </div>
                        <div style="color: #aaa; font-size: 1.2em;">
                            {{ message.time }}
                        </div>
                    </div>
                    <div style="color: #777; font-size: 0.9em; margin-top: 5px;">
                        {{ message.message }}
                    </div>
                </li>
                {% endfor %}
            </ul>
        </div>
        <!-- 右侧详细信息 -->
        <div id="detail" style="flex: 2; border: 1px solid #ccc; padding: 10px;">
            <h2><span id="detail-title">Details</span></h2>
            <hr/>
            <br>

            <div id="conversation-container" style="overflow-y: auto; max-height: 60vh;">
                <!-- 会话消息会被插入到这里 -->
            </div>

            <!-- 发送消息区域 -->
            <div id="message-input-area" style="margin-top: 20px;">
                <div class="floating-window-container" style="max-height: 150px; overflow-y: auto;">
                    <div id="floatingWindow" class="floating-window"
                         style=" border: 1px solid #ccc; display: none">
                        {{ quick_reply_html|safe }}
                    </div>
                </div>
                <button class="floating-button" onclick="toggleFloatingWindow()">Quick Reply</button>
                <label for="message-content"></label>
                <textarea id="message-content" rows="6" style="width: 100%; height: 150px;" data-id=""></textarea>

                <!-- 上传图片 -->
                <input type="file" id="message-images" accept="image/*" multiple onchange="previewImages()"
                       style="margin-top: 10px;">
                <div id="image-preview" style="display: flex; gap: 10px; margin-top: 10px;"></div>

                <button id="send-button" style="margin-top: 10px;" onclick="sendMessage()">Send</button>
            </div>

        </div>
    </div>
</div>

<!-- 图片放大模态框 -->
<div id="imageModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8); cursor: pointer;" onclick="closeImageModal()">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); max-width: 90%; max-height: 90%;">
        <img id="modalImage" style="max-width: 100%; max-height: 100%; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.5);">
        <div style="position: absolute; top: 10px; right: 10px; color: white; font-size: 30px; font-weight: bold; cursor: pointer; background: rgba(0,0,0,0.5); width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;" onclick="closeImageModal()">&times;</div>
    </div>
</div>

<script>
    const codeDict = {{ code_json|safe }};

    // 图片放大功能
    function openImageModal(imageSrc) {
        const modal = document.getElementById('imageModal');
        const modalImage = document.getElementById('modalImage');
        modalImage.src = imageSrc;
        modal.style.display = 'block';

        // 阻止事件冒泡
        event.stopPropagation();
    }

    function closeImageModal() {
        const modal = document.getElementById('imageModal');
        modal.style.display = 'none';
    }

    // 按ESC键关闭模态框
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeImageModal();
        }
    });

    function toggleFloatingWindow() {
        const floatingWindow = document.getElementById('floatingWindow');
        if (floatingWindow.style.display === 'block') {
            floatingWindow.style.display = 'none';
        } else {
            floatingWindow.style.display = 'block';
        }
    }

    function fillInputNew(dataId) {
        const messageInput = document.getElementById('message-content');
        messageInput.value = codeDict[dataId];
        toggleFloatingWindow(); // 选中快捷回复后自动关闭浮窗
    }


    function loadDetail(number, element) {
        // 移除之前选中的高亮样式
        const listItems = document.querySelectorAll('#conversation-list li');
        listItems.forEach(item => {
            item.style.backgroundColor = '#fff'; // 清空内联背景色
            item.style.border = '1px solid #ddd'; // 清空内联边框
        });

        // 添加当前选中的高亮样式
        element.style.backgroundColor = 'rgb(250 250 210)'; // 设置内联背景色
        element.style.border = '2px solid rgb(240 222 230)'; // 设置内联边框

        const url = "{% url 'admin:customer_service_detail' 'NUMBER' %}".replace('NUMBER', number);
        fetch(url)
            .then(response => response.json())
            .then(data => {
                const detailTitleDiv = document.getElementById('detail-title');
                detailTitleDiv.innerHTML = '<h2> Details: &nbsp;&nbsp;&nbsp;' + number + '</h2>'; // Clear previous content

                const containerDiv = document.getElementById('conversation-container');
                containerDiv.innerHTML = ''; // Clear previous content

                data.details.forEach(item => {
                    const messageDiv = document.createElement('div');
                    let content = item.content ? item.content.replace(/\n/g, '<br>').replace(/\\n/g, '<br>') : '';

                    // 处理图片URLs（新增的image_urls字段）
                    let imageContent = '';
                    if (item.image_urls && item.image_urls.length > 0) {
                        imageContent = item.image_urls.map(imageUrl => {
                            return `<img src="${imageUrl}" style="max-width: 120px; max-height: 120px; margin: 3px; border-radius: 6px; cursor: pointer; transition: transform 0.2s ease; display: inline-block;"
                                       onclick="openImageModal('${imageUrl}')"
                                       onmouseenter="this.style.transform='scale(1.05)'"
                                       onmouseleave="this.style.transform='scale(1)'"
                                       onerror="this.style.display='none'">`;
                        }).join('');
                    }

                    // 如果没有image_urls字段，回退到检查content中的图片URL（向后兼容）
                    if (!imageContent && content) {
                        const imageUrlPattern = /(https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp))/gi;
                        const isImageUrl = imageUrlPattern.test(content);

                        if (isImageUrl) {
                            const imageUrl = content.match(imageUrlPattern)[0];
                            imageContent = `<img src="${imageUrl}" style="max-width: 120px; max-height: 120px; border-radius: 6px; cursor: pointer; transition: transform 0.2s ease;"
                                           onclick="openImageModal('${imageUrl}')"
                                           onmouseenter="this.style.transform='scale(1.05)'"
                                           onmouseleave="this.style.transform='scale(1)'"
                                           onerror="this.style.display='none'">`;
                            content = ''; // 清空content，因为已经作为图片显示了
                        }
                    }

                    // 合并文字内容和图片内容
                    const finalContent = (content ? content : '') + (imageContent ? (content ? '<br>' : '') + imageContent : '');

                    if (item.is_right === true) {
                        messageDiv.style.textAlign = 'right';
                        messageDiv.innerHTML = `<div style="clear: both;"></div>
                    <div style="display: inline-block; text-align: left; background: #d1e7dd; border: 1px solid #ccc; padding: 10px; margin: 5px; border-radius: 10px; float: right; max-width: 70%;">
                        ${finalContent}
                    </div>
                    <div style="clear: both;"></div>`;

                    } else {
                        messageDiv.style.textAlign = 'left';
                        messageDiv.innerHTML = `<div style="clear: both;"></div>
                    <div style="display: inline-block; text-align: left; background: #f8d7da; border: 1px solid #ccc; padding: 10px; margin: 5px; border-radius: 10px; max-width: 70%;">
                        ${finalContent}
                    </div>
                    <div style="clear: both;"></div>`;
                    }
                    containerDiv.appendChild(messageDiv);
                });

                // 自动滚动到最新消息
                setTimeout(() => {
                    containerDiv.scrollTop = containerDiv.scrollHeight;
                }, 0);

                // 将 number 存储在 textarea 和按钮的 data-id 属性中
                document.getElementById('message-content').setAttribute('data-id', number);
                document.getElementById('send-button').setAttribute('data-id', number);
            })
            .catch(error => {
                console.error('Error fetching details:', error);
            });
    }

    function sendMessage() {
        const number = document.getElementById('message-content').getAttribute('data-id');

        const imagesInput = document.getElementById('message-images');
        const files = imagesInput.files;

        const messageContent = document.getElementById('message-content').value;

        if (!messageContent && files.length === 0) {
            alert('Please enter a message or upload an image');
            return;
        }

        const url = "{% url 'admin:customer_service_send_msg' %}";
        const formData = new FormData();
        formData.append('number', number);
        formData.append('content', messageContent);
        formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');

        // 添加图片
        Array.from(files).slice(0, 3).forEach((file, idx) => {
            formData.append('images', file);  // 后端用 request.FILES.getlist("images") 接收
        });

        fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        }).then(response => response.json())
            .then(data => {
                if (data.err_code !== 0) {
                    alert('Message sent failed:' + data.err_msg);
                    return;
                }

                const containerDiv = document.getElementById('conversation-container');

                // 自己发的文字
                if (messageContent) {
                    const messageDiv = document.createElement('div');
                    let content = messageContent.replace(/\n/g, '<br>');
                    messageDiv.style.textAlign = 'right';
                    messageDiv.innerHTML = `<div style="clear: both;"></div>
                        <div style="display: inline-block; text-align: left; background: #d1e7dd; border: 1px solid #ccc; padding: 10px; margin: 5px; border-radius: 10px; float: right; max-width: 70%;">
                            ${content}
                        </div>
                        <div style="clear: both;"></div>`;
                    containerDiv.appendChild(messageDiv);
                }

                // 自己发的图片（本地预览）
                console.log(files.length)
                if (files.length > 0) {
                    const imgDiv = document.createElement("div");
                    imgDiv.style.textAlign = "right";
                    Array.from(files).forEach(file => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const img = document.createElement("img");
                            img.src = e.target.result;  // 直接显示本地 DataURL
                            img.style.maxWidth = "120px";
                            img.style.maxHeight = "120px";
                            img.style.margin = "5px";
                            img.style.borderRadius = "6px";
                            img.style.cursor = "pointer";
                            img.style.transition = "transform 0.2s ease";

                            // 添加悬停效果
                            img.onmouseenter = function() {
                                this.style.transform = "scale(1.05)";
                            };
                            img.onmouseleave = function() {
                                this.style.transform = "scale(1)";
                            };

                            // 添加点击放大功能
                            img.onclick = function() {
                                openImageModal(e.target.result);
                            };

                            imgDiv.appendChild(img);

                            // 保证滚动条跟随到底部
                            setTimeout(() => {
                                containerDiv.scrollTop = containerDiv.scrollHeight;
                            }, 0);
                        };
                        reader.readAsDataURL(file);
                    });
                    containerDiv.appendChild(imgDiv);
                }

                document.getElementById('message-content').value = ''; // 清空输入框
                imagesInput.value = ''; // 清空图片选择
                document.getElementById('image-preview').innerHTML = ''; // 清空预览

                // 如果后端有返回内容
                if ('data' in data && 'backend_content' in data.data) {
                    const messageDiv = document.createElement('div');
                    let content = data.data.backend_content.replace(/\n/g, '<br>');
                    messageDiv.style.textAlign = 'left';
                    messageDiv.innerHTML = `<div style="clear: both;"></div>
                        <div style="display: inline-block; text-align: left; background: #f8d7da; border: 1px solid #ccc; padding: 10px; margin: 5px; border-radius: 10px; max-width: 70%;">
                            ${content}
                        </div>
                        <div style="clear: both;"></div>`;
                    containerDiv.appendChild(messageDiv);
                }

                setTimeout(() => {
                    containerDiv.scrollTop = containerDiv.scrollHeight;
                }, 0);
            })
            .catch(error => {
                console.error('Error sending message:', error);
                alert('Failed to send message');
            });
    }


    function toggleMenu(id) {
        const menu = document.getElementById(id);
        if (menu.classList.contains('show')) {
            menu.classList.remove('show');
        } else {
            menu.classList.add('show');
        }
    }


    function previewImages() {
        const input = document.getElementById('message-images');
        const preview = document.getElementById('image-preview');
        preview.innerHTML = ''; // 清空上一次的预览

        const files = Array.from(input.files).slice(0, 3); // 限制最多3张
        files.forEach(file => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.style.maxWidth = '100px';
                img.style.maxHeight = '100px';
                img.style.border = '1px solid #ccc';
                img.style.borderRadius = '6px';
                img.style.cursor = 'pointer';
                img.style.transition = 'transform 0.2s ease';

                // 添加悬停效果
                img.onmouseenter = function() {
                    this.style.transform = 'scale(1.05)';
                };
                img.onmouseleave = function() {
                    this.style.transform = 'scale(1)';
                };

                // 添加点击放大功能
                img.onclick = function() {
                    openImageModal(e.target.result);
                };

                preview.appendChild(img);
            };
            reader.readAsDataURL(file);
        });

        // 如果超过3张，清理掉多余的
        if (input.files.length > 3) {
            alert("最多只能上传3张图片！");
            input.value = ''; // 重置
        }
    }

</script>
{% endblock %}
