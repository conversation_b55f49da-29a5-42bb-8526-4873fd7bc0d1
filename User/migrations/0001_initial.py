# Generated by Django 3.2.5 on 2022-10-08 02:39

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Device',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.IntegerField(verbose_name='user id')),
                ('device_id', models.CharField(default='', max_length=50, verbose_name='device id')),
                ('token', models.CharField(default='DEFAULT-TOKEN-JONHALL', max_length=50, verbose_name='token')),
                ('jpush_id', models.CharField(blank=True, max_length=100, verbose_name='jpush id')),
                ('unbind', models.IntegerField(default=0, verbose_name='is unbind')),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='GuestUsage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('guest_user_id', models.IntegerField(verbose_name='guest user id')),
                ('ip', models.CharField(max_length=50, verbose_name='ip')),
                ('password', models.CharField(max_length=50, verbose_name='password')),
                ('status', models.CharField(max_length=20, verbose_name='status')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='GuestUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_id', models.CharField(max_length=50, verbose_name='device id')),
                ('expire_at', models.DateTimeField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.CharField(max_length=50, verbose_name='email')),
                ('password', models.CharField(max_length=50, verbose_name='password')),
                ('type', models.CharField(default='NORMAL', max_length=20, verbose_name='user type')),
                ('idfa', models.CharField(blank=True, max_length=200, verbose_name='idfa')),
                ('expired_at', models.DateTimeField(blank=True, verbose_name='expired_at')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
