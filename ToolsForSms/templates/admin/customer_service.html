{# templates/admin/customer_service.html #}
{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
<style>
    /* 按钮悬停效果 */
    .floating-button:hover {
        background-color: #0056b3 !important;
        transform: translateY(-1px);
        transition: all 0.2s ease;
    }

    .send-button:hover {
        background-color: #218838 !important;
        transform: translateY(-1px);
        transition: all 0.2s ease;
    }

    .send-button:disabled {
        background-color: #6c757d !important;
        cursor: not-allowed !important;
        transform: none !important;
    }

    /* 按钮容器样式 */
    .button-container {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;
        align-items: center;
    }

    /* 拖拽上传区域样式 */
    .drag-over {
        border-color: #007bff !important;
        background-color: #e3f2fd !important;
        transform: scale(1.02);
    }

    /* 可排序图片项样式 */
    .sortable-image-item {
        position: relative;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 8px;
        background-color: #fff;
        cursor: move;
        transition: all 0.3s ease;
        user-select: none;
    }

    .sortable-image-item:hover {
        border-color: #007bff;
        box-shadow: 0 4px 12px rgba(0,123,255,0.15);
        transform: translateY(-2px);
    }

    .sortable-image-item.dragging {
        opacity: 0.5;
        transform: rotate(5deg);
        z-index: 1000;
    }

    .sortable-image-item .image-preview {
        width: 100%;
        height: 120px;
        object-fit: cover;
        border-radius: 4px;
        margin-bottom: 8px;
    }

    .sortable-image-item .image-info {
        font-size: 12px;
        color: #6c757d;
        text-align: center;
        margin-bottom: 8px;
    }

    .sortable-image-item .image-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .sortable-image-item .drag-handle {
        cursor: move;
        font-size: 16px;
        color: #6c757d;
        padding: 4px;
    }

    .sortable-image-item .delete-btn {
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        cursor: pointer;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .sortable-image-item .delete-btn:hover {
        background: #c82333;
    }

    /* 拖拽占位符样式 */
    .drag-placeholder {
        border: 2px dashed #007bff;
        background-color: #e3f2fd;
        border-radius: 8px;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #007bff;
        font-weight: 500;
    }
</style>


<div class="container mt-4">
    <h1>Customer Service</h1>
    <div style="display: flex;">
        <!-- 左侧列表 -->
        <div style="flex: 1; border: 1px solid #ccc; padding: 10px; margin-right: 10px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); background-color: #f9f9f9;">
            <h2 style="text-align: center; font-family: Arial, sans-serif; color: #333;">Conversation List</h2>
            <ul id="conversation-list"
                style="list-style-type: none; padding: 0; margin: 0;  max-height: 70vh;  overflow-y: auto;">
                {% for message in conversation_list %}
                <li style="cursor: pointer; padding: 10px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 6px; background-color: #fff; transition: box-shadow 0.3s ease;"
                    onclick="loadDetail('{{ message.number }}', this)">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <span style="margin-right: 10px; font-weight: bold; color: #555;">{{ forloop.counter }}.</span>
                            <span style="color: #333;">{{ message.number }}</span>
                        </div>
                        <div style="color: #aaa; font-size: 1.2em;">
                            {{ message.time }}
                        </div>
                    </div>
                    <div style="color: #777; font-size: 0.9em; margin-top: 5px;">
                        {{ message.message }}
                    </div>
                </li>
                {% endfor %}
            </ul>
        </div>
        <!-- 右侧详细信息 -->
        <div id="detail" style="flex: 2; border: 1px solid #ccc; padding: 10px;">
            <h2><span id="detail-title">Details</span></h2>
            <hr/>
            <br>

            <div id="conversation-container" style="overflow-y: auto; max-height: 60vh;">
                <!-- 会话消息会被插入到这里 -->
            </div>

            <!-- 发送消息区域 -->
            <div id="message-input-area" style="margin-top: 20px;">
                <div class="floating-window-container" style="max-height: 150px; overflow-y: auto;">
                    <div id="floatingWindow" class="floating-window"
                         style=" border: 1px solid #ccc; display: none">
                        {{ quick_reply_html|safe }}
                    </div>
                </div>

                <!-- 快捷回复和发送按钮区域 -->
                <div class="button-container">
                    <button class="floating-button" onclick="toggleFloatingWindow()"
                            style="padding: 8px 16px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                        Quick Reply
                    </button>
                    <button id="send-button" class="send-button" onclick="sendMessage()"
                            style="padding: 8px 16px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: 500;">
                        <span class="send-button-text">发送消息</span>
                    </button>
                </div>

                <label for="message-content"></label>
                <textarea id="message-content" rows="6" style="width: 100%; height: 150px;" data-id=""></textarea>

                <!-- 证件图片上传区域 -->
                <div style="margin-top: 15px; padding: 12px; background-color: #f8f9fa; border-radius: 6px; border: 1px solid #e9ecef;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="margin: 0; color: #495057; font-size: 16px;">证件图片上传</h4>
                        <div style="display: flex; gap: 8px;">
                            <button type="button" onclick="selectMultipleImages()"
                                    style="padding: 6px 12px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                📁 选择图片
                            </button>
                            <button type="button" onclick="clearAllImages()"
                                    style="padding: 6px 12px; background-color: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                🗑️ 清空
                            </button>
                        </div>
                    </div>

                    <!-- 隐藏的文件输入框 -->
                    <input type="file" id="multiple-image-input" accept="image/*" multiple style="display: none;" onchange="handleMultipleImageUpload(this.files)">

                    <!-- 拖拽上传区域 -->
                    <div id="drag-drop-area"
                         style="border: 2px dashed #ced4da; border-radius: 6px; padding: 20px; text-align: center; background-color: #fff; margin-bottom: 15px; transition: all 0.3s ease;"
                         ondrop="handleDrop(event)"
                         ondragover="handleDragOver(event)"
                         ondragenter="handleDragEnter(event)"
                         ondragleave="handleDragLeave(event)">
                        <div style="color: #6c757d; font-size: 14px;">
                            <div style="font-size: 24px; margin-bottom: 8px;">📎</div>
                            <div style="font-weight: 500; margin-bottom: 4px;">拖拽图片到这里或点击上方按钮选择</div>
                            <div style="font-size: 12px;">支持多张图片同时上传，最多4张</div>
                        </div>
                    </div>

                    <!-- 图片预览和排序区域 -->
                    <div id="sortable-images-container" style="display: none;">
                        <div style="margin-bottom: 10px; color: #495057; font-size: 13px; font-weight: 500;">
                            📋 已上传图片（拖拽调整顺序）：
                        </div>
                        <div id="sortable-images" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                            <!-- 动态生成的图片预览项 -->
                        </div>
                        <div style="margin-top: 10px; font-size: 12px; color: #6c757d;">
                            💡 提示：拖拽图片可以调整顺序，点击 ❌ 可以删除图片
                        </div>
                    </div>
                    </div>
                </div>


            </div>

        </div>
    </div>
</div>

<!-- 图片放大模态框 -->
<div id="imageModal"
     style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8); cursor: pointer;"
     onclick="closeImageModal()">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); max-width: 90%; max-height: 90%;">
        <img id="modalImage"
             style="max-width: 100%; max-height: 100%; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.5);">
        <div style="position: absolute; top: 10px; right: 10px; color: white; font-size: 30px; font-weight: bold; cursor: pointer; background: rgba(0,0,0,0.5); width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;"
             onclick="closeImageModal()">&times;
        </div>
    </div>
</div>

<script>
    const codeDict = {{ code_json|safe }};

    // 图片放大功能
    function openImageModal(imageSrc) {
        const modal = document.getElementById('imageModal');
        const modalImage = document.getElementById('modalImage');
        modalImage.src = imageSrc;
        modal.style.display = 'block';

        // 阻止事件冒泡
        event.stopPropagation();
    }

    function closeImageModal() {
        const modal = document.getElementById('imageModal');
        modal.style.display = 'none';
    }

    // 按ESC键关闭模态框
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeImageModal();
        }
    });

    function toggleFloatingWindow() {
        const floatingWindow = document.getElementById('floatingWindow');
        if (floatingWindow.style.display === 'block') {
            floatingWindow.style.display = 'none';
        } else {
            floatingWindow.style.display = 'block';
        }
    }

    function fillInputNew(dataId) {
        const messageInput = document.getElementById('message-content');
        messageInput.value = codeDict[dataId];
        toggleFloatingWindow(); // 选中快捷回复后自动关闭浮窗
    }


    function loadDetail(number, element) {
        // 移除之前选中的高亮样式
        const listItems = document.querySelectorAll('#conversation-list li');
        listItems.forEach(item => {
            item.style.backgroundColor = '#fff'; // 清空内联背景色
            item.style.border = '1px solid #ddd'; // 清空内联边框
        });

        // 添加当前选中的高亮样式
        element.style.backgroundColor = 'rgb(250 250 210)'; // 设置内联背景色
        element.style.border = '2px solid rgb(240 222 230)'; // 设置内联边框

        const url = "{% url 'admin:customer_service_detail' 'NUMBER' %}".replace('NUMBER', number);
        fetch(url)
            .then(response => response.json())
            .then(data => {
                console.log('Received data:', data); // 调试信息

                const detailTitleDiv = document.getElementById('detail-title');
                detailTitleDiv.innerHTML = '<h2> Details: &nbsp;&nbsp;&nbsp;' + number + '</h2>'; // Clear previous content

                const containerDiv = document.getElementById('conversation-container');
                containerDiv.innerHTML = ''; // Clear previous content

                // 检查是否是错误响应
                if (data.err_code && data.err_code !== 0) {
                    console.error('Server error:', data.err_msg);
                    containerDiv.innerHTML = `<div style="padding: 20px; text-align: center; color: #d32f2f;">Error: ${data.err_msg}</div>`;
                    return;
                }

                // 检查数据结构
                if (!data || !data.details) {
                    console.error('Invalid data structure:', data);
                    containerDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No conversation data available or invalid data format.</div>';
                    return;
                }

                if (!Array.isArray(data.details)) {
                    console.error('data.details is not an array:', data.details);
                    containerDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Invalid conversation data format.</div>';
                    return;
                }

                if (data.details.length === 0) {
                    containerDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No messages found for this conversation.</div>';
                    return;
                }

                data.details.forEach(item => {
                    const messageDiv = document.createElement('div');
                    let content = item.content ? item.content.replace(/\n/g, '<br>').replace(/\\n/g, '<br>') : '';

                    // 处理图片URLs（新增的image_urls字段）
                    let imageContent = '';
                    if (item.image_urls && item.image_urls.length > 0) {
                        imageContent = item.image_urls.map(imageUrl => {
                            return `<img src="${imageUrl}" style="max-width: 120px; max-height: 120px; margin: 3px; border-radius: 6px; cursor: pointer; transition: transform 0.2s ease; display: inline-block;"
                                       onclick="openImageModal('${imageUrl}')"
                                       onmouseenter="this.style.transform='scale(1.05)'"
                                       onmouseleave="this.style.transform='scale(1)'"
                                       onerror="this.style.display='none'">`;
                        }).join('');
                    }

                    // 如果没有image_urls字段，回退到检查content中的图片URL（向后兼容）
                    if (!imageContent && content) {
                        const imageUrlPattern = /(https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp))/gi;
                        const isImageUrl = imageUrlPattern.test(content);

                        if (isImageUrl) {
                            const imageUrl = content.match(imageUrlPattern)[0];
                            imageContent = `<img src="${imageUrl}" style="max-width: 120px; max-height: 120px; border-radius: 6px; cursor: pointer; transition: transform 0.2s ease;"
                                           onclick="openImageModal('${imageUrl}')"
                                           onmouseenter="this.style.transform='scale(1.05)'"
                                           onmouseleave="this.style.transform='scale(1)'"
                                           onerror="this.style.display='none'">`;
                            content = ''; // 清空content，因为已经作为图片显示了
                        }
                    }

                    // 合并文字内容和图片内容
                    const finalContent = (content ? content : '') + (imageContent ? (content ? '<br>' : '') + imageContent : '');

                    if (item.is_right === true) {
                        messageDiv.style.textAlign = 'right';
                        messageDiv.innerHTML = `<div style="clear: both;"></div>
                    <div style="display: inline-block; text-align: left; background: #d1e7dd; border: 1px solid #ccc; padding: 10px; margin: 5px; border-radius: 10px; float: right; max-width: 70%;">
                        ${finalContent}
                    </div>
                    <div style="clear: both;"></div>`;

                    } else {
                        messageDiv.style.textAlign = 'left';
                        messageDiv.innerHTML = `<div style="clear: both;"></div>
                    <div style="display: inline-block; text-align: left; background: #f8d7da; border: 1px solid #ccc; padding: 10px; margin: 5px; border-radius: 10px; max-width: 70%;">
                        ${finalContent}
                    </div>
                    <div style="clear: both;"></div>`;
                    }
                    containerDiv.appendChild(messageDiv);
                });

                // 自动滚动到最新消息
                setTimeout(() => {
                    containerDiv.scrollTop = containerDiv.scrollHeight;
                }, 0);

                // 将 number 存储在 textarea 和按钮的 data-id 属性中
                document.getElementById('message-content').setAttribute('data-id', number);
                document.getElementById('send-button').setAttribute('data-id', number);

                if (data.details && data.details.length > 0) {
                    // 更新最后消息时间戳
                    const latestMessage = data.details.reduce((latest, current) => {
                        const latestTs = latest.latest_ts;
                        const currentTs = current.latest_ts;
                        return currentTs > latestTs ? current : latest;
                    }, data.details[0]);

                    lastMessageTimestamp = latestMessage.latest_ts;
                    console.log('after load details, Last message timestamp:', lastMessageTimestamp);
                }
            })
            .catch(error => {
                console.error('Error fetching details:', error);
                const containerDiv = document.getElementById('conversation-container');
                containerDiv.innerHTML = `<div style="padding: 20px; text-align: center; color: #d32f2f;">
                    Network Error: Failed to load conversation details.<br>
                    <small style="color: #666;">Please check your connection and try again.</small>
                </div>`;
            });
    }

    function sendMessage() {
        const number = document.getElementById('message-content').getAttribute('data-id');
        const messageContent = document.getElementById('message-content').value;

        // 获取所有上传的图片
        const imageFiles = getAllUploadedImages();

        // 检查是否输入了消息内容或图片
        if (!messageContent && imageFiles.length === 0) {
            alert('请输入消息内容或上传图片');
            return;
        }

        // 获取发送按钮并暂时禁用，防止重复点击
        const sendButton = document.getElementById('send-button');
        const originalText = sendButton.innerHTML;
        sendButton.disabled = true;
        sendButton.innerHTML = '<span class="send-button-text">发送中...</span>';

        const url = "{% url 'admin:customer_service_send_msg' %}";
        const formData = new FormData();
        formData.append('number', number);
        formData.append('content', messageContent);

        lastMessageTimestamp = new Date().getTime();
        formData.append('latest_ts', lastMessageTimestamp);
        formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');

        // 添加图片（按用户排序的顺序）
        imageFiles.forEach(file => {
            formData.append('images', file);
        });

        fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        }).then(response => response.json())
            .then(data => {
                // 恢复按钮状态
                sendButton.disabled = false;
                sendButton.innerHTML = originalText;

                if (data.err_code !== 0) {
                    alert('消息发送失败: ' + data.err_msg);
                    return;
                }

                const containerDiv = document.getElementById('conversation-container');

                // 自己发的文字
                if (messageContent) {
                    const messageDiv = document.createElement('div');
                    let content = messageContent.replace(/\n/g, '<br>');
                    messageDiv.style.textAlign = 'right';
                    messageDiv.innerHTML = `<div style="clear: both;"></div>
                        <div style="display: inline-block; text-align: left; background: #d1e7dd; border: 1px solid #ccc; padding: 10px; margin: 5px; border-radius: 10px; float: right; max-width: 70%;">
                            ${content}                    </div>
                        <div style="clear: both;"></div>`;
                    containerDiv.appendChild(messageDiv);
                }

                // 自己发的图片（本地预览）
                if (imageFiles.length > 0) {
                    const imgDiv = document.createElement("div");
                    imgDiv.style.textAlign = "right";
                    imageFiles.forEach(file => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const img = document.createElement("img");
                            img.src = e.target.result;  // 直接显示本地 DataURL
                            img.style.maxWidth = "120px";
                            img.style.maxHeight = "120px";
                            img.style.margin = "5px";
                            img.style.borderRadius = "6px";
                            img.style.cursor = "pointer";
                            img.style.transition = "transform 0.2s ease";

                            // 添加悬停效果
                            img.onmouseenter = function() {
                                this.style.transform = "scale(1.05)";
                            };
                            img.onmouseleave = function() {
                                this.style.transform = "scale(1)";
                            };

                            // 添加点击放大功能
                            img.onclick = function() {
                                openImageModal(e.target.result);
                            };

                            imgDiv.appendChild(img);

                            // 保证滚动条跟随到底部
                            setTimeout(() => {
                                containerDiv.scrollTop = containerDiv.scrollHeight;
                            }, 0);
                        };
                        reader.readAsDataURL(file);
                    });
                    containerDiv.appendChild(imgDiv);
                }

                // 清空输入框和图片选择
                document.getElementById('message-content').value = '';
                clearAllImages(); // 清空所有上传的图片

                // 重置轮询计数器
                pollingCount = 0;
                // 启动轮询, 查看是否有新消息
                startPolling(number);

                setTimeout(() => {
                    containerDiv.scrollTop = containerDiv.scrollHeight;
                }, 0);
            })
            .catch(error => {
                console.error('Error sending message:', error);
                alert('发送消息失败，请重试');
                // 恢复按钮状态
                sendButton.disabled = false;
                sendButton.innerHTML = originalText;
            });
    }


    function toggleMenu(id) {
        const menu = document.getElementById(id);
        if (menu.classList.contains('show')) {
            menu.classList.remove('show');
        } else {
            menu.classList.add('show');
        }
    }

    // 按ESC键关闭模态框
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeImageModal();
        }
    });



    let pollingInterval = null;
    let lastMessageTimestamp = 0;
    const maxPollingCount = 80; // 最大轮询次数
    let currentChatNumber = null;

    function startPolling(number) {
            // 停止之前的轮询
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            currentChatNumber = number;
            pollingCount = 0; // 重置计数器

            // 开始新的轮询，每秒检查一次
            pollingInterval = setInterval(() => {
            if (pollingCount >= maxPollingCount) {
                stopPolling();
                showPollingTimeoutNotification();
                return;
            }

            checkForNewMessages(number);
            pollingCount++;
        }, 1000);
    }

    function stopPolling() {
        if (pollingInterval) {
            clearInterval(pollingInterval);
            pollingInterval = null;
        }
    }

    function showPollingTimeoutNotification() {
        const pollingTimeoutSeconds = maxPollingCount;
        const message = `已经轮询了 ${pollingTimeoutSeconds} 秒，暂时无法获取最新消息。\n\n可能的原因：\n• 网络连接不稳定\n• 服务器响应较慢\n• 当前没有新消息\n\n是否要刷新页面重新开始？`;

        if (confirm(message)) {
            // 用户选择刷新页面
            window.location.reload();
        } else {
            // 用户选择不刷新，可以手动重新开始轮询
            console.log('用户选择不刷新页面，轮询已停止');
        }
    }


    function checkForNewMessages(number) {
        console.log('Checking for new messages... ' , number, lastMessageTimestamp);

        if (!number || !lastMessageTimestamp) return;

        const url = "{% url 'admin:check_new_messages' %}";
        fetch(`${url}?number=${encodeURIComponent(number)}&latest_ts=${lastMessageTimestamp}`)
            .then(response => response.json())
            .then(data => {
                if (data.err_code === 0 && data.has_new) {
                     // 停止轮训
                    stopPolling();

                    // 有新消息，重新加载对话详情
                    loadDetail(number, document.querySelector(`#conversation-list li[onclick*="'${number}'"]`));
                }
            })
            .catch(error => {
                console.error('检查新消息失败:', error);
            });
    }


    // 页面卸载时停止轮询
    window.addEventListener('beforeunload', () => {
        stopPolling();
    });

    // ==================== 多图片上传和拖拽排序功能 ====================

    let uploadedImages = []; // 存储上传的图片信息
    const maxImages = 4; // 最大图片数量

    // 选择多张图片
    function selectMultipleImages() {
        document.getElementById('multiple-image-input').click();
    }

    // 处理多图片上传
    function handleMultipleImageUpload(files) {
        if (!files || files.length === 0) return;

        // 检查图片数量限制
        const remainingSlots = maxImages - uploadedImages.length;
        if (files.length > remainingSlots) {
            alert(`最多只能上传 ${maxImages} 张图片，当前还可以上传 ${remainingSlots} 张`);
            return;
        }

        // 处理每个文件
        Array.from(files).forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                addImageToList(file);
            } else {
                console.warn('跳过非图片文件:', file.name);
            }
        });

        updateImageDisplay();
    }

    // 添加图片到列表
    function addImageToList(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const imageData = {
                id: Date.now() + Math.random(), // 唯一ID
                file: file,
                name: file.name,
                size: file.size,
                dataUrl: e.target.result,
                type: detectImageType(file.name) // 自动检测图片类型
            };

            uploadedImages.push(imageData);
            updateImageDisplay();
        };
        reader.readAsDataURL(file);
    }

    // 检测图片类型
    function detectImageType(filename) {
        const name = filename.toLowerCase();
        if (name.includes('身份证') || name.includes('id') || name.includes('sfz')) {
            if (name.includes('背面') || name.includes('back') || name.includes('反面')) {
                return '身份证背面';
            } else {
                return '身份证正面';
            }
        } else if (name.includes('驾驶证') || name.includes('driver') || name.includes('jsz')) {
            if (name.includes('副页') || name.includes('back') || name.includes('副面')) {
                return '驾驶证副页';
            } else {
                return '驾驶证正页';
            }
        }
        return '其他证件';
    }

    // 更新图片显示
    function updateImageDisplay() {
        const container = document.getElementById('sortable-images-container');
        const imagesGrid = document.getElementById('sortable-images');

        if (uploadedImages.length === 0) {
            container.style.display = 'none';
            return;
        }

        container.style.display = 'block';
        imagesGrid.innerHTML = '';

        uploadedImages.forEach((image, index) => {
            const imageItem = createImageItem(image, index);
            imagesGrid.appendChild(imageItem);
        });

        // 重新初始化拖拽排序
        initializeSortable();
    }

    // 创建图片项元素
    function createImageItem(image, index) {
        const div = document.createElement('div');
        div.className = 'sortable-image-item';
        div.draggable = true;
        div.dataset.imageId = image.id;

        div.innerHTML = `
            <img src="${image.dataUrl}" alt="${image.name}" class="image-preview" onclick="openImageModal('${image.dataUrl}')">
            <div class="image-info">
                <div style="font-weight: 500; margin-bottom: 2px;">${image.type}</div>
                <div>${image.name}</div>
                <div>${(image.size / 1024).toFixed(1)} KB</div>
            </div>
            <div class="image-actions">
                <span class="drag-handle" title="拖拽排序">⋮⋮</span>
                <button class="delete-btn" onclick="removeImage('${image.id}')" title="删除图片">❌</button>
            </div>
        `;

        return div;
    }

    // 删除图片
    function removeImage(imageId) {
        uploadedImages = uploadedImages.filter(img => img.id != imageId);
        updateImageDisplay();
    }

    // 清空所有图片
    function clearAllImages() {
        if (uploadedImages.length === 0) return;

        if (confirm('确定要清空所有图片吗？')) {
            uploadedImages = [];
            updateImageDisplay();
        }
    }

    // 初始化拖拽排序
    function initializeSortable() {
        const container = document.getElementById('sortable-images');
        let draggedElement = null;
        let placeholder = null;

        // 为每个图片项添加拖拽事件
        container.querySelectorAll('.sortable-image-item').forEach(item => {
            item.addEventListener('dragstart', handleDragStart);
            item.addEventListener('dragend', handleDragEnd);
            item.addEventListener('dragover', handleDragOver);
            item.addEventListener('drop', handleDrop);
        });

        function handleDragStart(e) {
            draggedElement = this;
            this.classList.add('dragging');

            // 创建占位符
            placeholder = document.createElement('div');
            placeholder.className = 'drag-placeholder';
            placeholder.innerHTML = '放置到这里';

            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', this.outerHTML);
        }

        function handleDragEnd(e) {
            this.classList.remove('dragging');

            // 移除占位符
            if (placeholder && placeholder.parentNode) {
                placeholder.parentNode.removeChild(placeholder);
            }

            draggedElement = null;
            placeholder = null;
        }

        function handleDragOver(e) {
            if (e.preventDefault) {
                e.preventDefault();
            }

            e.dataTransfer.dropEffect = 'move';

            if (this !== draggedElement) {
                const rect = this.getBoundingClientRect();
                const midpoint = rect.left + rect.width / 2;

                if (e.clientX < midpoint) {
                    this.parentNode.insertBefore(placeholder, this);
                } else {
                    this.parentNode.insertBefore(placeholder, this.nextSibling);
                }
            }

            return false;
        }

        function handleDrop(e) {
            if (e.stopPropagation) {
                e.stopPropagation();
            }

            if (draggedElement !== this) {
                // 获取新的排序
                const allItems = Array.from(container.querySelectorAll('.sortable-image-item:not(.dragging)'));
                const placeholderIndex = Array.from(container.children).indexOf(placeholder);
                const draggedId = draggedElement.dataset.imageId;

                // 更新数组顺序
                const draggedImage = uploadedImages.find(img => img.id == draggedId);
                uploadedImages = uploadedImages.filter(img => img.id != draggedId);
                uploadedImages.splice(placeholderIndex, 0, draggedImage);

                // 重新渲染
                updateImageDisplay();
            }

            return false;
        }
    }

    // ==================== 拖拽上传功能 ====================

    // 处理拖拽上传
    function handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();

        const dragDropArea = document.getElementById('drag-drop-area');
        dragDropArea.classList.remove('drag-over');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleMultipleImageUpload(files);
        }
    }

    function handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function handleDragEnter(e) {
        e.preventDefault();
        e.stopPropagation();

        const dragDropArea = document.getElementById('drag-drop-area');
        dragDropArea.classList.add('drag-over');
    }

    function handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();

        // 只有当离开整个拖拽区域时才移除样式
        const dragDropArea = document.getElementById('drag-drop-area');
        const rect = dragDropArea.getBoundingClientRect();

        if (e.clientX < rect.left || e.clientX > rect.right ||
            e.clientY < rect.top || e.clientY > rect.bottom) {
            dragDropArea.classList.remove('drag-over');
        }
    }

    // ==================== 修改发送消息函数以支持新的图片上传方式 ====================

    // 获取所有上传的图片文件（替换原来的获取方式）
    function getAllUploadedImages() {
        return uploadedImages.map(img => img.file);
    }

</script>
{% endblock %}
