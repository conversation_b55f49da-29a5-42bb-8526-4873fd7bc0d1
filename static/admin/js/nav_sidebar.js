/* Django 3.2.25 官方 nav_sidebar.js */
document.addEventListener('DOMContentLoaded', function() {
    var toggleBtn = document.getElementById('toggle-nav-sidebar');
    var navSidebar = document.getElementById('nav-sidebar');
    var adminMain = document.querySelector('.admin-main');

    if (!toggleBtn || !navSidebar || !adminMain) {
        return;
    }

    // 切换导航栏显示/隐藏
    toggleBtn.addEventListener('click', function() {
        navSidebar.classList.toggle('toggled');
        adminMain.classList.toggle('toggled');
        // 保存状态到 localStorage
        localStorage.setItem('djangoNavSidebarToggled', navSidebar.classList.contains('toggled'));
    });

    // 从 localStorage 恢复状态
    var savedState = localStorage.getItem('djangoNavSidebarToggled');
    if (savedState === 'true') {
        navSidebar.classList.add('toggled');
        adminMain.classList.add('toggled');
    }

    // 处理窗口大小变化
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 992) {
            navSidebar.classList.remove('toggled');
            adminMain.classList.remove('toggled');
        }
    });
});