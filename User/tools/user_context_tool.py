import json

from AiInsuranceBot.settings import logger
from User.tools.user_tool import UserTool


class UserContextTool:
    @staticmethod
    def get_user_context_str(email: str, content: str, device_id: str) -> str:
        user_ctx_dict = UserContextTool.get_user_context_dict(email, content, device_id)
        user_ctx_str = json.dumps(user_ctx_dict, indent=4, default=str, ensure_ascii=False)
        return user_ctx_str

    @staticmethod
    def get_user_context_dict(email: str, content: str, device_id: str) -> dict:
        try:
            user = UserTool.get_user_by_device_id(device_id)
            if not user:
                return {}
            user_context_dict = {
                "用户id": user.id,
                "用户设备": device_id,
                "邮箱": email,
                "反馈内容": content,
                "创建时间": user.created_at,
            }
            return user_context_dict
        except Exception:
            logger.error(f"[UserContextTool] failed for {device_id}, content: {content}", exc_info=True)
            return {}
