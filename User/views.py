import json

from AiInsuranceBot.settings import logger
from Common.timeutil import TimeUtil
from Common.views import CommonView
from Sms.tools.tool_image import ImageUtil
from Sms.tools.tool_it_support_sms import SmsItSupportTool


class TaskResultNotify(CommonView):

    def post(self, request):
        try:
            data, err_code, err_msg = self.ReadPostJson(request, ("taskid", "result_filepath",))
            if err_code != 0:
                logger.error(f"[TaskResultNotify] param error:  {request.body}")
                return self.ReturnError(err_code, err_msg)

            taskid = data["taskid"]
            result_filepath = data["result_filepath"]
            result_text = data["res_text"]
            logger.info(
                f"[TaskResultNotify] taskid: {taskid}, result_filepath:{result_filepath}, res_text:{result_text}")
            ai_number = taskid.split("_")[1]
            ai_device_id = taskid.split("_")[1]

            # 不一定有图片，可能只有error message
            if result_filepath:
                with open(result_filepath, 'rb') as f:
                    result_image_content = f.read()
                    logger.info(f"[TaskResultNotify] read bytes len：{len(result_image_content)}")  # 能正常拿到完整数据

                filename_abs, image_url = ImageUtil.save_image(result_image_content, 'png')
                # 存储消息
                SmsItSupportTool.add_support_sms_mms_both_from_ai(ai_device_id, ai_number, result_text,
                                                                  json.dumps([image_url]), TimeUtil.GetNowTsInInt())
            else:
                # 存储消息
                SmsItSupportTool.add_support_sms_mms_both_from_ai(ai_device_id, ai_number, result_text,
                                                                  "", TimeUtil.GetNowTsInInt())

            logger.info(f"[TaskResultNotify] taskid: {taskid}, result_filepath:{result_filepath}, success")
            return self.ReturnSuccess({})
        except Exception as e:
            logger.error(f"[TaskResultNotify] error: {e}", exc_info=True)
            return self.ReturnError(-1, "server notify error")
