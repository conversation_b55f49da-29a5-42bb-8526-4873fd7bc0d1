import datetime
import json
import re
import threading

from django.contrib import admin
from django.http import JsonResponse
from django.template.response import TemplateResponse
from django.urls import path

from AiInsuranceBot import settings
from AiInsuranceBot.settings import logger
from Common.timeutil import TimeUtil
from Sms.tools.tool_image import ImageUtil
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_sms import SmsTool
from ToolsForSms.models import CustomerServiceTool
from User.tools.user_context_tool import UserContextTool
from rpa import rpa_robot


def extract_numbers(s):
    # 匹配所有数字字符，组合成新字符串
    return ''.join(re.findall(r'\d', s))


@admin.register(CustomerServiceTool)
class CustomerServiceToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/customer_service.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('customer_service_detail/<str:number>/', self.admin_site.admin_view(self.customer_service_detail),
                 name='customer_service_detail'),
            path('customer_service_send_msg/', self.admin_site.admin_view(self.customer_service_send_msg),
                 name='customer_service_send_msg'),
            path('check_new_messages/', self.admin_site.admin_view(self.check_new_messages),
                 name='check_new_messages'),
        ]
        return custom_urls + urls

    @staticmethod
    def check_new_messages(request):
        try:
            if request.method == 'GET':
                ai_number = request.GET.get('number')
                latest_ts = request.GET.get('latest_ts')
                logger.info(f"[check_new_messages] ai_number:{ai_number}, latest_ts:{latest_ts}")

                if not ai_number or not latest_ts:
                    return JsonResponse({"err_code": -1, "err_msg": "缺少参数"})

                latest_ts = int(latest_ts)

                # 查询最新消息
                records = SmsTool.get_ai_all_send_sms(ai_number, datetime.datetime.now() - datetime.timedelta(days=90))

                if records:
                    # 获取最新消息的时间戳
                    newest_record = max(records, key=lambda x: x.latest_ts)
                    if newest_record.latest_ts > latest_ts:
                        return JsonResponse({
                            "err_code": 0,
                            "has_new": True,
                            "newest_ts": newest_record.latest_ts
                        })

                return JsonResponse({
                    "err_code": 0,
                    "has_new": False
                })

        except Exception as e:
            logger.error(f"[CustomerServiceToolAdmin] 检查新消息时出错: {e}", exc_info=True)
            return JsonResponse({"err_code": -1, "err_msg": "服务器异常"})

    def changelist_view(self, request, extra_context=None):
        try:
            # 这里加载你想要在初始化时加载的数据
            extra_context = CustomerServiceToolAdmin.get_conversations()

            reply_data_path = settings.BASE_DIR + '/Config/reply_list.json'
            try:
                with open(reply_data_path, encoding="utf-8") as f:
                    reply_data = json.loads(f.read())
            except Exception:
                logger.error(f"[CustomerServiceToolAdmin] failed to load replyList: {reply_data_path}")
                reply_data = []

            code_dict = {}
            item_counter = 0

            def generate_menu(items):
                nonlocal item_counter
                html = "<ul>"
                for item in items:
                    indent = item['level'] * 20
                    if "children" in item:
                        html += (f'<li onclick="toggleMenu(\'{item["label"]}-menu\')" style="margin-left: {indent}px;">'
                                 f'{item["label"]}')
                        html += f'<ul id="{item["label"]}-menu" class="nested-menu">'
                        html += generate_menu(item["children"])
                        html += '</ul></li>'
                    elif "code" in item:
                        data_id = f'item-{item_counter}'
                        code_dict[data_id] = item["code"]
                        html += (f'<li onclick="fillInputNew(\'{data_id}\')" style="margin-left: {indent}px;" '
                                 f'data-id="{data_id}">{item["label"]}</li>')
                        item_counter += 1
                html += "</ul>"
                return html

            quick_reply_html = generate_menu(reply_data)
            extra_context["quick_reply_html"] = quick_reply_html
            extra_context["code_json"] = json.dumps(code_dict)
            return super().changelist_view(request, extra_context=extra_context)
        except Exception as e:
            logger.error(f"[CustomerServiceToolAdmin] error: {e}", exc_info=True)
            return TemplateResponse(request, 'admin/customer_service.html', {})

    def customer_service_detail(self, request, number):
        try:
            logger.info(f"{request.META}")
            details = self.get_conversation_details(number)
            return JsonResponse({"details": details})
        except Exception as e:
            logger.error(f"[CustomerServiceToolAdmin] error: {e}", exc_info=True)
            return JsonResponse({"err_code": -1, "err_msg": "failed: " + str(e)})

    def customer_service_send_msg(self, request):
        """
        客服发送消息主入口函数
        """
        try:
            if request.method != 'POST':
                return JsonResponse({"err_code": -1, "err_msg": "只支持POST请求"})

            # 1. 验证基础参数
            validation_result = self._validate_request_params(request)
            if validation_result["err_code"] != 0:
                return JsonResponse(validation_result)

            data = request.POST
            to_ai_number = data["number"]
            content = data["content"]
            latest_ts = data["latest_ts"]
            images = request.FILES.getlist("images")

            # 2. 获取设备ID
            device_result = self._get_device_info(to_ai_number)
            if device_result["err_code"] != 0:
                return JsonResponse(device_result)
            to_ai_device_id = device_result["device_id"]

            # 3. 处理图片
            image_result = self._process_images(images)
            if image_result["err_code"] != 0:
                return JsonResponse(image_result)

            image_urls = image_result["image_urls"]
            image_path_list = image_result["image_path_list"]
            if len(image_urls) < 3:
                return JsonResponse({"err_code": -1, "err_msg": "图片数小于3张，最小应该是3张"})

            # 4. 解析指令
            cmd_result = self._parse_command(content)
            if cmd_result["err_code"] != 0:
                return JsonResponse(cmd_result)

            cmd_json = cmd_result["cmd_json"]

            # 5. 关联图片路径到指令
            self._associate_images_to_command(cmd_json, image_path_list)

            # 6. 保存消息并调用机器人
            return self._save_message_and_call_robot(to_ai_device_id, to_ai_number, content,
                                                     image_urls, latest_ts, cmd_json)

        except Exception as e:
            logger.error(f"[CustomerServiceToolAdmin] error", exc_info=True)
            return JsonResponse({"err_code": -1, "err_msg": "服务器异常，请联系IT看日志！"})

    def _validate_request_params(self, request):
        """
        验证请求参数
        """
        data = request.POST
        content = data.get("content")
        images = request.FILES.getlist("images")
        latest_ts = data.get("latest_ts")

        # 检查是否有内容或图片
        if not content and not images:
            return {"err_code": -1, "err_msg": "需要指令+3张图片"}

        if not latest_ts:
            return {"err_code": -1, "err_msg": "没有携带时间戳"}

        return {"err_code": 0}

    def _get_device_info(self, to_ai_number):
        """
        获取设备信息
        """
        to_ai_device_id = SmsTool.get_device_id_from_mock_number(to_ai_number)

        if not to_ai_device_id:
            logger.error(f"[CustomerServiceToolAdmin] failed device_id not exists, to_ai_number:{to_ai_number}")
            return {"err_code": -1, "err_msg": "请先选择一个机器人开始聊天"}

        return {"err_code": 0, "device_id": to_ai_device_id}

    def _process_images(self, images):
        """
        处理上传的图片
        """
        image_urls = []
        image_data_list = []
        image_path_list = []

        if not images:
            return {"err_code": 0, "image_data_list": image_data_list,
                    "image_urls": image_urls, "image_path_list": image_path_list}

        try:
            for image_file in images:
                # 读取图片文件内容
                image_data = image_file.read()
                image_data_list.append(image_data)

                # 获取文件扩展名
                file_format = image_file.name.split('.')[-1].lower() if '.' in image_file.name else 'jpg'

                # 保存图片并获取URL
                filename_abs, image_url = ImageUtil.save_image(image_data, file_format)
                if image_url:
                    image_urls.append(image_url)
                    image_path_list.append(filename_abs)
                else:
                    logger.error(f"[CustomerServiceToolAdmin] failed to save image: {image_file.name}")
                    return {"err_code": -1, "err_msg": f"Failed to save image: {image_file.name}"}

        except Exception as e:
            logger.error(f"[CustomerServiceToolAdmin] error processing images: {e}", exc_info=True)
            return {"err_code": -1, "err_msg": f"处理图片时发生错误: {str(e)}"}

        return {"err_code": 0, "image_data_list": image_data_list,
                "image_urls": image_urls, "image_path_list": image_path_list}

    def _parse_command(self, content) -> dict:
        """
        解析AI指令
        """
        # 拿到AI指令集合
        if not settings.ai_commands:
            return {"err_code": -1, "err_msg": "未配置AI指令集，请联系IT"}

        # 获取AI指令
        line_list = content.split("\n")
        line_list = [v.strip() for v in line_list if v]
        if len(line_list) < 2:
            return {"err_code": -1, "err_msg": "第一行指令，第二行车主号码"}

        cmd = line_list[0].upper()
        if cmd not in settings.ai_commands:
            return {"err_code": -1, "err_msg": f"指令:{cmd} 不存在，请检查"}

        cmd_json = settings.ai_commands[cmd].copy()  # 创建副本避免修改原始配置

        # 车主号码
        car_owner_number = extract_numbers(line_list[1])
        if not car_owner_number:
            return {"err_code": -1, "err_msg": f"第一行指令，第二行要填车主号码！"}

        cmd_json["phone_number"] = car_owner_number

        # 转移日期
        if len(line_list) > 2:
            transfer_date = extract_numbers(line_list[2].strip())
            cmd_json["djzy_date"] = transfer_date

        logger.info(f"[customer_service_send_msg] get ai cmd done: {cmd}, json:{cmd_json}")

        return {"err_code": 0, "cmd_json": cmd_json}

    def _associate_images_to_command(self, cmd_json, image_path_list):
        """
        关联图片路径到指令JSON
        """
        # 权宜之计，固定好图片顺序，顺序是：身份证正，身份证反，驾驶证
        if len(image_path_list) >= 1:
            cmd_json["sfz_image_path"] = image_path_list[0]
        if len(image_path_list) >= 2:
            cmd_json["sfz_image_path2"] = image_path_list[1]
        if len(image_path_list) >= 3:
            cmd_json["jsz_image_path"] = image_path_list[2]
        if len(image_path_list) > 3:
            cmd_json["jsz_image_path2"] = image_path_list[3]
        else:
            cmd_json["jsz_image_path2"] = image_path_list[2] if len(image_path_list) >= 3 else ""

    def _save_message_and_call_robot(self, to_ai_device_id, to_ai_number, content,
                                     image_urls, latest_ts, cmd_json: dict):
        """
        保存消息并调用机器人
        """
        try:
            # 存储消息
            SmsItSupportTool.add_support_sms_mms_both_from_human(
                to_ai_device_id, to_ai_number, content,
                json.dumps(image_urls), latest_ts)
            logger.info(f"[customer_service_send_msg] save message done")

            # 调用机器人
            logger.info(f"[customer_service_send_msg] start rap bot...")
            cmd_json["taskid"] = str(TimeUtil.GetBeijingTimeShortStr(TimeUtil.GetNow()) + "_" + to_ai_number)

            t = threading.Thread(target=self.call_rap_robot, args=(cmd_json,))
            t.start()

            return JsonResponse({"err_code": 0, "err_msg": "success"})

        except Exception as e:
            logger.error(f"[CustomerServiceToolAdmin] error saving message or calling robot: {e}", exc_info=True)
            return JsonResponse({"err_code": -1, "err_msg": f"保存消息或调用机器人时出错: {str(e)}"})

    @staticmethod
    def call_rap_robot(cmd_json: dict):
        url = "https://m.ins10.cn/#/sticsale/agent?tabbar=true&icon=true"  # 目标网页
        rpa_robot(url, cmd_json)

    @staticmethod
    def get_conversation_details(number) -> list:
        device_id = SmsTool.get_device_id_from_mock_number(number)

        logger.info(f"[CustomerServiceToolAdmin] device_id:{device_id}, number: {number}")

        # 构建查询条件
        before_date = datetime.datetime.now() - datetime.timedelta(days=90)
        records = SmsTool.get_it_by_both_number(device_id, number, before_date)
        logger.info(f"[CustomerServiceToolAdmin] device_id:{device_id}, size:{len(records)}")
        for index, i in enumerate(records):
            logger.info(f"[CustomerServiceToolAdmin] index:{index}, {i.from_number}->{i.to_number}: {i.content}")

        res = []
        for r in records:
            content = r.content
            if r.from_number == settings.APP_IT_SUPPORT_SHOW_PHONE:
                is_right = True
            else:
                is_right = False

            # 安全地解析图片URLs
            image_urls = []
            if r.images:
                try:
                    image_urls = json.loads(r.images)
                    if not isinstance(image_urls, list):
                        image_urls = []
                    image_urls = [v.replace("http://127.0.0.1:8000/uploads/", "http://**************/static/uploads/")
                                  for v in image_urls]
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"[CustomerServiceToolAdmin] Failed to parse images JSON: {r.images}, error: {e}")
                    # 如果解析失败，尝试作为单个URL处理
                    if isinstance(r.images, str) and r.images.strip():
                        image_urls = [r.images.strip()]
                    else:
                        image_urls = []

            d = {
                "is_right": is_right,
                "from_number": r.from_number,
                "to_number": r.to_number,
                "created_at": r.created_at,
                "direction": r.direction,
                "content": f"[{TimeUtil.GetBeijingTimeStr(r.created_at)}]\n{content if content else '[空白文本]'}",
                "image_urls": image_urls,
                "latest_ts": r.latest_ts,
            }

            res.append(d)

        return res

    @staticmethod
    def get_conversations() -> dict:
        # 最近的对话
        logger.info(f"[CustomerServiceToolAdmin] start...")
        before_date = datetime.datetime.now() - datetime.timedelta(days=30)
        records = SmsTool.get_records_by_customer_service(settings.APP_IT_SUPPORT_DEVICEID, before_date)
        group_records = {}
        logger.info(f"[CustomerServiceToolAdmin] get_conversations before size: {len(records)}")

        for r in records:
            if r.from_number == settings.APP_IT_SUPPORT_SHOW_PHONE:
                number = r.to_number
            else:
                number = r.from_number

            if number not in group_records:
                group_records[number] = [r]
            else:
                group_records[number].append(r)

        # 自己的号码就不用看了
        if settings.APP_IT_SUPPORT_SHOW_PHONE in group_records:
            del group_records[settings.APP_IT_SUPPORT_SHOW_PHONE]

        tt = {}
        un_read_size = 0
        for from_number, _records in group_records.items():
            logger.info(f"[CustomerServiceToolAdmin] {from_number}, size: {len(_records)}")

            count_rs_after_last_s = 0
            last_r_ts = -1
            last_content = ""

            # 排除掉纯系统通知的group
            user_send_cnt = len([v for v in _records if v.direction == settings.SMS_DIRECTION_RECEIVE])
            if user_send_cnt == 0:
                continue

            for item in reversed(_records):
                if item.direction == settings.SMS_DIRECTION_RECEIVE:
                    last_r_ts = max(item.latest_ts, last_r_ts)
                    count_rs_after_last_s += 1
                    if not last_content:
                        last_content = item.content
                elif item.direction == settings.SMS_DIRECTION_SEND:
                    if last_r_ts < 0:
                        last_r_ts = item.latest_ts
                    if not last_content:
                        last_content = "[客服回复] " + item.content
                    break

            # 计算未读数量
            if count_rs_after_last_s > 0:
                un_read_size += 1

            tt[from_number] = {
                "no_read_cnt": count_rs_after_last_s,
                "latest_ts": last_r_ts,
                "last_content": last_content,
            }
        logger.info(f"[CustomerServiceToolAdmin] beforeConversationSize: {len(group_records)}, "
                    f"afterConversationSize: {len(tt)},  noReadSize: {un_read_size}")
        sorted_res = dict(sorted(tt.items(), key=CustomerServiceToolAdmin.custom_sort))

        messages = []
        for k, v in sorted_res.items():
            messages.append({
                "head": k[0:3],
                "number": k,
                "time": TimeUtil.GetBeijingTimeStrFromTs(v['latest_ts']),
                "message": v["last_content"],
                "tips": v["no_read_cnt"] if "no_read_cnt" in v else 0
            })

        context = {
            "conversation_start_from": TimeUtil.GetBeijingTimeStr(before_date),
            "conversation_list": messages,
            "conversation_size": len(tt),
            "conversation_unread_size": un_read_size,
        }
        return context

    @staticmethod
    def custom_sort(item):
        # 根据 "no_read_cnt" 的值来排序，no_read_cnt 大的排在前面
        if item[1]["no_read_cnt"] > 0:
            return 0, -item[1]["latest_ts"]
        else:
            return 1, -item[1]["latest_ts"]

    @staticmethod
    def get_user_ctx(to_device_id: str, to_number: str) -> str:
        logger.info(f"[CustomerServiceToolAdmin] backend door get context: {to_device_id}:{to_number}")
        user_profile_ctx_str = UserContextTool.get_user_context_str("", "", to_device_id)
        return user_profile_ctx_str

    @staticmethod
    def get_user_it_sms_list(to_device_id: str, to_number: str) -> str:
        logger.info(f"[CustomerServiceToolAdmin] backend door get sms list: {to_device_id}:{to_number}")
        records = SmsTool.get_latest_top_n_it_sms(to_device_id, 50)

        res = []
        for v in records:
            time_ts = v.created_at.strftime('%Y-%m-%d %H:%M:%S')
            _content = v.content if v.content else v.images
            res.append(f"[{time_ts}][{v.from_number}]->[{v.to_number}]:{_content}")
        if len(res) == 0:
            res.append("该用户没有短信!")
        return json.dumps(res, indent=4, default=str, ensure_ascii=False)

    @staticmethod
    def get_user_non_it_sms_list(to_device_id: str, to_number: str) -> str:
        logger.info(f"[CustomerServiceToolAdmin] backend door get sms list: {to_device_id}:{to_number}")
        records = SmsTool.get_latest_top_n_non_it_sms(to_device_id, 50)

        res = []
        for v in records:
            time_ts = v.created_at.strftime('%Y-%m-%d %H:%M:%S')
            _content = v.content if v.content else v.images
            res.append(f"[{time_ts}][{v.from_number}]->[{v.to_number}]:{_content}")
        if len(res) == 0:
            res.append("该用户没有短信!")
        return json.dumps(res, indent=4, default=str, ensure_ascii=False)
