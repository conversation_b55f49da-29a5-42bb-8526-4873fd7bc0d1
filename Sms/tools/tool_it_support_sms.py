import re
import threading
from typing import Union

import AiInsuranceBot.logid_middleware
from AICustomerService.ai_customer_service import AiCustomerServiceUtil
from AiInsuranceBot import settings
from AiInsuranceBot.settings import logger
from Common.timeutil import TimeUtil
from Sms.models import SMSRecord
from User.tools.user_tool import UserTool


class SmsItSupportTool:

    @staticmethod
    def _is_it_support_phone(number: str) -> bool:
        return number == settings.APP_IT_SUPPORT_SHOW_PHONE

    @staticmethod
    def _get_to_device_id(to_number: str) -> Union[str, None]:
        if to_number[0:4] == settings.APP_IT_SUPPORT_NO_NUMBER_PREFIX:
            logger.info(f"[SmsItSupportTool] _get_to_userid:{to_number} is user_id prefix")
            return UserTool.get_device_id_from_mock_number(to_number)
        return None

    @staticmethod
    def complete_ticket_url(content):
        try:
            # 定义正则表达式模式，捕获所有包含 TICKET_URL 的情况
            pattern = re.compile(r'[\{\[\(\<]*TICKET_URL[\}\]\)\>\{]*')

            def replacer(match):
                return '{{TICKET_URL}}'

            # 使用正则表达式的 sub 方法进行替换
            corrected_content = pattern.sub(replacer, content)

            return corrected_content
        except Exception:
            logger.error(f"[SmsItSupportTool] complete_ticket_url failed: {content}", exc_info=True)
            return content

    @staticmethod
    def set_ai_answer(thread_logid: str, device_id: str, query_number: str, query: str):
        try:
            # 设置logid
            AiInsuranceBot.logid_middleware.logid_context.set(thread_logid)

            if not query_number:
                query_number = UserTool.create_mock_number(device_id)

            logger.info(f"[SmsItSupportTool] set_ai_answer, device_id:{device_id}, query_number:{query_number}, "
                        f"query:{query}")

            if "http://" in query or "https://" in query:
                answer = "Hello! You have sent a link or image. To better assist you and ensure your materials are promptly forwarded to the technical department, we have more suitable channels for uploading these files. Please click here to submit a ticket, and we will respond to you within 24 to 48 hours. Thank you! {{TICKET_URL}}"
            else:
                answer = AiCustomerServiceUtil.get_ai_answer(query)

            if not answer:
                answer = "Hello! All our agents are currently busy now. Could you please try again in 2 minutes or submit a ticket here: {{TICKET_URL}} Our tech team will review your request and process it within 24-48 hours. Please note that changing numbers is not done at will due to the associated costs, and each request will be considered on a case-by-case basis. If you have any specific concerns or reasons for the change, you can include them in your ticket for the tech team's consideration."
                logger.error(f"[SmsItSupportTool] default ai answer: {answer}")
            else:
                if "{{TICKET_URL}}" not in answer:
                    answer = answer + "\n\n\nIf you have any further questions, you can contact our engineering team through a ticket: {{TICKET_URL}} Our tech team will review your request and process it within 24-48 hours."

            link = ""
            if "{{TICKET_URL}}" in answer:
                link = f"{settings.STATIC_URL_BASE}/submit_ticket/index.html?wxid={device_id}"
                answer = answer.replace("{{TICKET_URL}}", link)
            if "[TICKET_URL]" in answer:
                link = f"{settings.STATIC_URL_BASE}/submit_ticket/index.html?wxid={device_id}"
                answer = answer.replace("[TICKET_URL]", link)

            # 处理下 bad case
            if "TICKET_URL" in answer:
                _answer = answer
                answer = SmsItSupportTool.complete_ticket_url(answer)
                link = f"{settings.STATIC_URL_BASE}/submit_ticket/index.html?wxid={device_id}"
                answer = answer.replace("{{TICKET_URL}}", link)
                logger.error(f"[SmsItSupportTool] ai bad case: device_id:{device_id},before: {_answer},after:{answer}")

            if link:
                logger.info(
                    f"[SmsItSupportTool] set_ai_answer, device_id:{device_id},  "
                    f"query_number:{query_number}, query:{query}, answer:{answer}")
                SmsItSupportTool.add_support_sms_both_from_it_with_link(to_device_id=device_id,
                                                                        to_number=query_number,
                                                                        content=answer, link=link)
            else:
                logger.info(f"[SmsItSupportTool] set_ai_answer, device_id:{device_id}, query_number:{query_number}, "
                            f"query:{query}, answer:{answer}")
                SmsItSupportTool.add_support_sms_both_from_it_with_link(to_device_id=device_id, to_number=query_number,
                                                                        content=answer, link="")
        except Exception:
            logger.error(f"[SmsItSupportTool] set_ai_answer, device_id:{device_id}, query_number:{query_number}, "
                         f"query:{query}", exc_info=True)

    @staticmethod
    def it_support(device_id: str, from_number: str, to_number: str, content: str, latest_ts: int):
        try:
            if SmsItSupportTool._is_it_support_phone(to_number):
                SmsItSupportTool.add_support_sms_both_from_feedback(device_id, from_number, content)

                # AI 异步发送
                t = threading.Thread(target=SmsItSupportTool.set_ai_answer,
                                     args=(AiInsuranceBot.logid_middleware.logid_context.get(),
                                           device_id, from_number, content,))
                t.start()

                return True

            if SmsItSupportTool._is_it_support_phone(from_number):
                to_device_id = SmsItSupportTool._get_to_device_id(to_number)
                if to_device_id:
                    SmsItSupportTool.add_support_sms_both_from_it(to_device_id, to_number, content)
                    return True

            return False
        except Exception:
            logger.error(f"[SmsItSupportTool] it support:{device_id}, {from_number}->{to_number}, "
                         f"content:{content}, latest_ts:{latest_ts}, failed", exc_info=True)
            return False

    @staticmethod
    def mms_it_support(device_id: str, from_number: str, to_number: str, image_url: str, latest_ts: int):
        try:
            if SmsItSupportTool._is_it_support_phone(to_number):
                SmsItSupportTool.add_support_mms_both_from_feedback(device_id, from_number, image_url)

                # AI 异步发送
                t = threading.Thread(target=SmsItSupportTool.set_ai_answer,
                                     args=(AiInsuranceBot.logid_middleware.logid_context.get(),
                                           device_id, from_number, image_url,))
                t.start()

                return True

            if SmsItSupportTool._is_it_support_phone(from_number):
                to_userid = SmsItSupportTool._get_to_device_id(to_number)
                if to_userid:
                    SmsItSupportTool.add_support_mms_both_from_it(to_userid, to_number, image_url)
                    return True

            return False
        except Exception:
            logger.error(f"[SmsItSupportTool] it support:{device_id}, {from_number}->{to_number}, "
                         f"image_url:{image_url},latest_ts:{latest_ts}, failed", exc_info=True)
            return False

    @staticmethod
    def add_support_sms_both_from_feedback(feedback_device_id: str, feedback_from_number: str, content: str):
        if not feedback_from_number:
            feedback_from_number = UserTool.create_mock_number(feedback_device_id)

        it_support_device_id = settings.APP_IT_SUPPORT_DEVICEID

        latest_ts = TimeUtil.GetNowTsInInt()
        SmsItSupportTool.add_support_sms(feedback_device_id, settings.SMS_DIRECTION_SEND,
                                         feedback_from_number, settings.APP_IT_SUPPORT_SHOW_PHONE, content)
        SmsItSupportTool.add_support_sms(it_support_device_id, settings.SMS_DIRECTION_RECEIVE,
                                         feedback_from_number, settings.APP_IT_SUPPORT_SHOW_PHONE, content,
                                         latest_ts=latest_ts + 1)
        logger.warning(f"[SmsItSupportTool] sms feedback->it, {feedback_device_id}:{feedback_from_number}:{content}")

    @staticmethod
    def add_support_mms_both_from_feedback(feedback_device_id: str, feedback_from_number: str, image_url: str):
        if not feedback_from_number:
            feedback_from_number = UserTool.create_mock_number(feedback_device_id)

        it_support_device_id = settings.APP_IT_SUPPORT_DEVICEID
        SmsItSupportTool.add_support_sms(feedback_device_id, settings.SMS_DIRECTION_SEND,
                                         feedback_from_number, settings.APP_IT_SUPPORT_SHOW_PHONE, '', image_url)
        SmsItSupportTool.add_support_sms(it_support_device_id, settings.SMS_DIRECTION_RECEIVE,
                                         feedback_from_number, settings.APP_IT_SUPPORT_SHOW_PHONE, '', image_url)
        logger.warning(f"[SmsItSupportTool] mms feedback->it, {feedback_device_id}:{feedback_from_number}:{image_url}")

    @staticmethod
    def add_support_sms_both_from_it(to_device_id: str, to_number: str, content: str):
        from_device_id = settings.APP_IT_SUPPORT_DEVICEID
        SmsItSupportTool.add_support_sms(from_device_id, settings.SMS_DIRECTION_SEND,
                                         settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, content)
        SmsItSupportTool.add_support_sms(to_device_id, settings.SMS_DIRECTION_RECEIVE,
                                         settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, content)
        logger.warning(
            f"[SmsItSupportTool] sms it->user, to_device_id:{to_device_id}, to:{to_number}, content:{content}")

    @staticmethod
    def add_support_sms_mms_both_from_human(to_ai_device_id: str, to_ai_number: str, content: str, images: str,
                                            latest_ts: int):
        from_human_device_id = settings.APP_IT_SUPPORT_DEVICEID
        SmsItSupportTool.add_support_sms(from_human_device_id, settings.SMS_DIRECTION_SEND,
                                         settings.APP_IT_SUPPORT_SHOW_PHONE, to_ai_number, content, images, latest_ts)
        SmsItSupportTool.add_support_sms(to_ai_device_id, settings.SMS_DIRECTION_RECEIVE,
                                         settings.APP_IT_SUPPORT_SHOW_PHONE, to_ai_number, content, images, latest_ts)
        logger.warning(
            f"[SmsItSupportTool] sms human->ai, ai_device_id:{to_ai_device_id}, to:{to_ai_number}, content:{content},"
            f"images: {images}")

    @staticmethod
    def add_support_sms_mms_both_from_ai(from_ai_device_id: str, from_ai_number: str, content: str, images: str,
                                         latest_ts: int):
        to_human_device_id = settings.APP_IT_SUPPORT_DEVICEID
        SmsItSupportTool.add_support_sms(from_ai_device_id, settings.SMS_DIRECTION_SEND,
                                         from_ai_number, settings.APP_IT_SUPPORT_SHOW_PHONE, content, images, latest_ts)
        SmsItSupportTool.add_support_sms(to_human_device_id, settings.SMS_DIRECTION_RECEIVE,
                                         from_ai_number, settings.APP_IT_SUPPORT_SHOW_PHONE, content, images, latest_ts)
        logger.warning(
            f"[SmsItSupportTool] sms ai->human, ai_device_id:{from_ai_device_id}, from:{from_ai_number}, content:{content},"
            f"images: {images}")

    @staticmethod
    def add_support_mms_both_from_it(to_device_id: str, to_number: str, image_url: str):
        from_device_id = settings.APP_IT_SUPPORT_DEVICEID
        SmsItSupportTool.add_support_sms(from_device_id, settings.SMS_DIRECTION_SEND,
                                         settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, '', image_url)
        SmsItSupportTool.add_support_sms(to_device_id, settings.SMS_DIRECTION_RECEIVE,
                                         settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, '', image_url)

        logger.warning(
            f"[SmsItSupportTool] mms it->user, device_id:{to_device_id}, to:{to_number}, image_url:{image_url}")

    @staticmethod
    def add_support_sms_both_from_it_with_link(to_device_id: str, to_number: str, content: str, link: str):
        try:
            from_device_id = settings.APP_IT_SUPPORT_DEVICEID
            SmsItSupportTool.add_support_sms_with_link(from_device_id, settings.SMS_DIRECTION_SEND,
                                                       settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, content, link)
            SmsItSupportTool.add_support_sms_with_link(to_device_id, settings.SMS_DIRECTION_RECEIVE,
                                                       settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, content, link)
            logger.info(f"[SmsItSupportTool] sms it reply user:{to_device_id}, to:{to_number}, content:{content}")
        except Exception:
            logger.error(f"[SmsItSupportTool] sms it reply user:{to_device_id}:{to_number}, content:{content} failed",
                         exc_info=True)

    @staticmethod
    def add_support_sms(device_id: str, direction: str, from_number: str, to_number: str, content: str,
                        images_urls='', latest_ts=None):
        if direction == settings.SMS_DIRECTION_SEND:
            status = "delivered"
        else:
            status = "received"

        logger.info(
            f'[add_support_sms] direction:{direction}, device_id:{device_id}, {from_number}->{to_number}, '
            f'content={content}, images_urls={images_urls}, status:{status}')
        try:
            if not latest_ts:
                latest_ts = TimeUtil.GetNowTsInInt()
            record = SMSRecord(
                device_id=device_id, direction=direction.upper(), from_number=from_number,
                to_number=to_number,
                content=content, images=images_urls, latest_ts=latest_ts,
            )
            record.save()
        except Exception:
            logger.error(f"[add_support_sms] device_id:{device_id}, from:{from_number}, to:{to_number}, "
                         f"content:{content}, save error: ", exc_info=True)

    @staticmethod
    def add_support_sms_without_push(device_id: str, direction: str, from_number: str, to_number: str, content: str,
                                     images_urls=None, latest_ts=None):
        if direction == settings.SMS_DIRECTION_SEND:
            status = "delivered"
        else:
            status = "received"

        logger.info(f'[add_support_sms_without_push] direction:{direction}, device_id:{device_id}, '
                    f'{from_number}->{to_number}, content={content}, images_urls={images_urls}, status:{status}')
        try:
            if not latest_ts:
                latest_ts = TimeUtil.GetNowTsInInt()
            record = SMSRecord(
                device_id=device_id, direction=direction.upper(), from_number=from_number,
                to_number=to_number,
                content=content, images=images_urls, latest_ts=latest_ts,
            )
            record.save()

        except Exception:
            logger.error(f"[add_support_sms_without_push] device_id:{device_id}, from:{from_number}, to:{to_number}, "
                         f"content:{content}, save error: ", exc_info=True)

    @staticmethod
    def add_support_sms_with_link(device_id: str, direction: str, from_number: str, to_number: str, content: str,
                                  link: str, images_urls=None, latest_ts=None):
        logger.info(
            f'[add_support_sms_with_link]  direction:{direction}, device_id:{device_id}, '
            f'{from_number}->{to_number}, content={content}, images_urls={images_urls}, '
            f'link={link}')
        try:
            if not latest_ts:
                latest_ts = TimeUtil.GetNowTsInInt()
            record = SMSRecord(
                device_id=device_id, direction=direction.upper(), from_number=from_number,
                to_number=to_number,
                content=content, images=images_urls, latest_ts=latest_ts,
                link=link
            )
            record.save()

        except Exception:
            logger.error(f"[add_support_sms_with_link] device_id:{device_id}, from:{from_number}, to:{to_number}, "
                         f"content:{content}, save error: ", exc_info=True)
